'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartOptions,
  ChartData
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PriceDataPoint {
  timestamp: number;
  price: number;
  volume?: number;
}

interface PriceChartProps {
  data: PriceDataPoint[];
  coinName: string;
  coinSymbol: string;
  timeframe: string;
  height?: number;
  showVolume?: boolean;
  className?: string;
}

const PriceChart: React.FC<PriceChartProps> = ({
  data,
  coinName,
  coinSymbol,
  timeframe,
  height = 400,
  showVolume = false,
  className = ''
}) => {
  const chartRef = useRef<ChartJS<'line'>>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Check for dark mode
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // Watch for dark mode changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  // Prepare chart data
  const chartData: ChartData<'line'> = {
    labels: data.map(point => {
      const date = new Date(point.timestamp);
      if (timeframe === '24h' || timeframe === '1h') {
        return date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      } else if (timeframe === '7d' || timeframe === '30d') {
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });
      } else {
        return date.toLocaleDateString('en-US', { 
          year: 'numeric',
          month: 'short' 
        });
      }
    }),
    datasets: [
      {
        label: `${coinName} Price`,
        data: data.map(point => point.price),
        borderColor: '#3B82F6',
        backgroundColor: isDarkMode 
          ? 'rgba(59, 130, 246, 0.1)' 
          : 'rgba(59, 130, 246, 0.05)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 6,
        pointHoverBackgroundColor: '#3B82F6',
        pointHoverBorderColor: '#FFFFFF',
        pointHoverBorderWidth: 2,
      }
    ]
  };

  // Chart options
  const options: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
        titleColor: isDarkMode ? '#F9FAFB' : '#111827',
        bodyColor: isDarkMode ? '#F9FAFB' : '#111827',
        borderColor: isDarkMode ? '#374151' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          title: (context) => {
            const dataIndex = context[0].dataIndex;
            const timestamp = data[dataIndex].timestamp;
            const date = new Date(timestamp);
            return date.toLocaleString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            });
          },
          label: (context) => {
            const value = context.parsed.y;
            return `${coinSymbol}: $${value.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: value < 1 ? 6 : 2
            })}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          color: isDarkMode ? '#9CA3AF' : '#6B7280',
          maxTicksLimit: 8
        },
        border: {
          display: false
        }
      },
      y: {
        display: true,
        position: 'right',
        grid: {
          color: isDarkMode ? '#374151' : '#F3F4F6',
          drawBorder: false
        },
        ticks: {
          color: isDarkMode ? '#9CA3AF' : '#6B7280',
          callback: function(value) {
            const numValue = Number(value);
            if (numValue >= 1000000) {
              return `$${(numValue / 1000000).toFixed(1)}M`;
            } else if (numValue >= 1000) {
              return `$${(numValue / 1000).toFixed(1)}K`;
            } else if (numValue < 1) {
              return `$${numValue.toFixed(6)}`;
            } else {
              return `$${numValue.toFixed(2)}`;
            }
          }
        },
        border: {
          display: false
        }
      }
    },
    elements: {
      point: {
        hoverRadius: 8
      }
    }
  };

  // Calculate price change
  const firstPrice = data[0]?.price || 0;
  const lastPrice = data[data.length - 1]?.price || 0;
  const priceChange = lastPrice - firstPrice;
  const priceChangePercent = firstPrice > 0 ? (priceChange / firstPrice) * 100 : 0;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Chart Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {coinName} ({coinSymbol})
            </h3>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-2xl font-bold text-gray-900 dark:text-white">
                ${lastPrice.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: lastPrice < 1 ? 6 : 2
                })}
              </span>
              <span className={`flex items-center text-sm font-medium ${
                priceChange >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {priceChange >= 0 ? '↗' : '↘'}
                {priceChangePercent >= 0 ? '+' : ''}{priceChangePercent.toFixed(2)}%
                <span className="ml-1 text-gray-500 dark:text-gray-400">
                  ({timeframe})
                </span>
              </span>
            </div>
          </div>
          
          {/* Time frame selector could go here */}
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="p-6">
        <div style={{ height: `${height}px` }}>
          {data.length > 0 ? (
            <Line
              ref={chartRef}
              data={chartData}
              options={options}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-500 dark:text-gray-400">
                  Loading chart data...
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Chart Footer with additional info */}
      {showVolume && data.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              24h Volume: ${data[data.length - 1]?.volume?.toLocaleString() || 'N/A'}
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              Data points: {data.length}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PriceChart;
