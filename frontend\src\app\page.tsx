'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import MarketOverview from '@/components/dashboard/MarketOverview';
import PriceChart from '@/components/charts/PriceChart';
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface PriceDataPoint {
  timestamp: number;
  price: number;
  volume?: number;
}

interface TradingSignal {
  type: 'BUY' | 'SELL' | 'HOLD' | 'STRONG_BUY' | 'STRONG_SELL';
  confidence: number;
  reason: string;
  coinId: string;
  coinName: string;
}

export default function Dashboard() {
  const [bitcoinData, setBitcoinData] = useState<PriceDataPoint[]>([]);
  const [ethereumData, setEthereumData] = useState<PriceDataPoint[]>([]);
  const [tradingSignals, setTradingSignals] = useState<TradingSignal[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    generateMockData();
  }, []);

  const generateMockData = () => {
    // Generate mock Bitcoin price data for the last 24 hours
    const now = Date.now();
    const btcData: PriceDataPoint[] = [];
    const ethData: PriceDataPoint[] = [];

    let btcPrice = 45234.67;
    let ethPrice = 2834.45;

    for (let i = 24; i >= 0; i--) {
      const timestamp = now - (i * 60 * 60 * 1000); // Every hour

      // Add some realistic price movement
      btcPrice += (Math.random() - 0.5) * 1000;
      ethPrice += (Math.random() - 0.5) * 100;

      btcData.push({
        timestamp,
        price: Math.max(btcPrice, 40000), // Keep price reasonable
        volume: Math.random() * 1000000000
      });

      ethData.push({
        timestamp,
        price: Math.max(ethPrice, 2000), // Keep price reasonable
        volume: Math.random() * 500000000
      });
    }

    setBitcoinData(btcData);
    setEthereumData(ethData);

    // Generate mock trading signals
    const signals: TradingSignal[] = [
      {
        type: 'STRONG_BUY',
        confidence: 85,
        reason: 'RSI oversold, bullish MACD crossover, strong support level',
        coinId: 'bitcoin',
        coinName: 'Bitcoin'
      },
      {
        type: 'HOLD',
        confidence: 65,
        reason: 'Neutral RSI, sideways trend, waiting for breakout',
        coinId: 'ethereum',
        coinName: 'Ethereum'
      },
      {
        type: 'BUY',
        confidence: 72,
        reason: 'Breaking resistance, increasing volume, positive sentiment',
        coinId: 'solana',
        coinName: 'Solana'
      },
      {
        type: 'SELL',
        confidence: 78,
        reason: 'Overbought RSI, bearish divergence, approaching resistance',
        coinId: 'cardano',
        coinName: 'Cardano'
      }
    ];

    setTradingSignals(signals);
    setLoading(false);
  };

  const getSignalColor = (type: string): string => {
    switch (type) {
      case 'STRONG_BUY':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'BUY':
        return 'bg-green-50 text-green-700 border-green-100';
      case 'HOLD':
        return 'bg-yellow-50 text-yellow-700 border-yellow-100';
      case 'SELL':
        return 'bg-red-50 text-red-700 border-red-100';
      case 'STRONG_SELL':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-100';
    }
  };

  const getSignalIcon = (type: string) => {
    switch (type) {
      case 'STRONG_BUY':
      case 'BUY':
        return <ArrowTrendingUpIcon className="h-5 w-5" />;
      case 'SELL':
      case 'STRONG_SELL':
        return <ArrowTrendingDownIcon className="h-5 w-5" />;
      case 'HOLD':
        return <ExclamationTriangleIcon className="h-5 w-5" />;
      default:
        return <CheckCircleIcon className="h-5 w-5" />;
    }
  };

  return (
    <Layout>
      <div className="space-y-8">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Real-time cryptocurrency market overview and analytics
          </p>
        </div>

        {/* Market Overview */}
        <MarketOverview />

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Bitcoin Chart */}
          <PriceChart
            data={bitcoinData}
            coinName="Bitcoin"
            coinSymbol="BTC"
            timeframe="24h"
            height={300}
            showVolume={true}
          />

          {/* Ethereum Chart */}
          <PriceChart
            data={ethereumData}
            coinName="Ethereum"
            coinSymbol="ETH"
            timeframe="24h"
            height={300}
            showVolume={true}
          />
        </div>

        {/* Trading Signals */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              AI Trading Signals
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Algorithmic analysis and recommendations based on technical indicators
            </p>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="animate-pulse space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                    </div>
                    <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {tradingSignals.map((signal, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg border ${getSignalColor(signal.type)}`}>
                        {getSignalIcon(signal.type)}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {signal.coinName}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getSignalColor(signal.type)}`}>
                            {signal.type.replace('_', ' ')}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {signal.reason}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {signal.confidence}% confidence
                      </div>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${signal.confidence}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Portfolio Performance
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Value</span>
                <span className="font-medium text-gray-900 dark:text-white">$12,456.78</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">24h Change</span>
                <span className="font-medium text-green-600">+$234.56 (1.92%)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total P&L</span>
                <span className="font-medium text-green-600">+$1,456.78 (13.2%)</span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Active Alerts
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">BTC > $46,000</span>
                <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">ETH < $2,800</span>
                <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                  Triggered
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">SOL +5%</span>
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Triggered
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Market Sentiment
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Overall</span>
                <span className="font-medium text-green-600">Bullish</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Social</span>
                <span className="font-medium text-yellow-600">Neutral</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Technical</span>
                <span className="font-medium text-green-600">Strong Buy</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
