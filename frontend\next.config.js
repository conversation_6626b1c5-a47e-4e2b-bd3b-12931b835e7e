/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // Configure images domain for external images
  images: {
    domains: [
      'via.placeholder.com',
      'assets.coingecko.com',
      'coin-images.coingecko.com',
      'cryptocompare.com'
    ],
    // Enable image optimization
    formats: ['image/webp', 'image/avif'],
  },

  // Environment variables that should be available on the client side
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
    NEXT_PUBLIC_APP_NAME: 'ChainScope',
    NEXT_PUBLIC_APP_VERSION: '1.0.0',
  },
  
  // Configure webpack for better bundle optimization
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Add custom webpack configurations here if needed
    
    // Optimize bundle size
    if (!dev && !isServer) {
      config.optimization.splitChunks.chunks = 'all';
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      };
    }
    
    return config;
  },
  
  // Configure headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // Configure redirects if needed
  async redirects() {
    return [
      // Add redirects here if needed
    ];
  },
  
  // Configure rewrites for API proxy if needed
  async rewrites() {
    return [
      // Proxy API requests to backend in development
      ...(process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_API_URL ? [
        {
          source: '/api/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`,
        },
      ] : []),
    ];
  },

  // Enable experimental features
  experimental: {
    // Enable optimized package imports
    optimizePackageImports: [
      '@heroicons/react',
      'lucide-react',
      'chart.js',
      'react-chartjs-2',
    ],
  },
  
  // Configure TypeScript
  typescript: {
    // Type checking is handled by the IDE and CI/CD pipeline
    ignoreBuildErrors: false,
  },
  
  // Configure ESLint
  eslint: {
    // ESLint is handled by the IDE and CI/CD pipeline
    ignoreDuringBuilds: false,
  },
  
  // Configure output for Docker builds
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,
  
  // Configure compression
  compress: true,
  
  // Configure trailing slash behavior
  trailingSlash: false,
  
  // Configure powered by header
  poweredByHeader: false,
  
  // Configure development indicators
  devIndicators: {
    position: 'bottom-right',
  },
};

module.exports = nextConfig;
