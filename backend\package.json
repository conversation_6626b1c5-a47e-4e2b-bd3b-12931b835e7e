{"name": "chainscope-backend", "version": "1.0.0", "description": "ChainScope - Advanced Cryptocurrency On-Chain Analytics Platform Backend", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["cryptocurrency", "blockchain", "analytics", "on-chain", "api"], "author": "HectorTa1989", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^3.0.3", "prisma": "^5.7.0", "redis": "^4.6.0", "socket.io": "^4.7.0", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "ws": "^8.14.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.19.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.0"}}