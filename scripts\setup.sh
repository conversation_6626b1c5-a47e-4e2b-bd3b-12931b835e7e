#!/bin/bash

# ChainScope Setup Script
# This script sets up the development environment for ChainScope

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="18.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js version $NODE_VERSION is compatible"
            return 0
        else
            print_error "Node.js version $NODE_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to check npm version
check_npm_version() {
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm version $NPM_VERSION found"
        return 0
    else
        print_error "npm is not installed"
        return 1
    fi
}

# Function to check Docker
check_docker() {
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker version $DOCKER_VERSION found"
        
        if command_exists docker-compose; then
            COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
            print_success "Docker Compose version $COMPOSE_VERSION found"
        else
            print_warning "Docker Compose not found. Some features may not work."
        fi
        return 0
    else
        print_warning "Docker not found. Docker setup will be skipped."
        return 1
    fi
}

# Function to setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "Created backend/.env from example"
        else
            print_warning "backend/.env.example not found"
        fi
    else
        print_warning "backend/.env already exists, skipping"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        cat > frontend/.env.local << EOF
# Frontend Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=ChainScope
NEXT_PUBLIC_APP_VERSION=1.0.0
EOF
        print_success "Created frontend/.env.local"
    else
        print_warning "frontend/.env.local already exists, skipping"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Install shared dependencies if exists
    if [ -d "shared" ]; then
        print_status "Installing shared dependencies..."
        cd shared
        if [ -f "package.json" ]; then
            npm install
        fi
        cd ..
    fi
    
    print_success "All dependencies installed successfully"
}

# Function to build shared modules
build_shared() {
    if [ -d "shared" ] && [ -f "shared/package.json" ]; then
        print_status "Building shared modules..."
        cd shared
        if npm run build 2>/dev/null; then
            print_success "Shared modules built successfully"
        else
            print_warning "Shared modules build failed or no build script found"
        fi
        cd ..
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    if command_exists docker && command_exists docker-compose; then
        print_status "Starting database services with Docker..."
        docker-compose up -d postgres redis
        
        # Wait for database to be ready
        print_status "Waiting for database to be ready..."
        sleep 10
        
        # Run database migrations if available
        if [ -f "backend/package.json" ]; then
            cd backend
            if npm run migrate 2>/dev/null; then
                print_success "Database migrations completed"
            else
                print_warning "No migration script found or migration failed"
            fi
            cd ..
        fi
    else
        print_warning "Docker not available. Please set up PostgreSQL and Redis manually."
        print_warning "PostgreSQL: localhost:5432, Database: chainscope"
        print_warning "Redis: localhost:6379"
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Test backend
    if [ -f "backend/package.json" ]; then
        cd backend
        if npm test 2>/dev/null; then
            print_success "Backend tests passed"
        else
            print_warning "Backend tests failed or no test script found"
        fi
        cd ..
    fi
    
    # Test frontend
    if [ -f "frontend/package.json" ]; then
        cd frontend
        if npm test -- --watchAll=false 2>/dev/null; then
            print_success "Frontend tests passed"
        else
            print_warning "Frontend tests failed or no test script found"
        fi
        cd ..
    fi
}

# Function to display final instructions
display_instructions() {
    print_success "Setup completed successfully!"
    echo ""
    echo "🚀 ChainScope is ready to run!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Start the development servers:"
    echo "     npm run dev"
    echo ""
    echo "  2. Or start individual services:"
    echo "     Backend:  cd backend && npm run dev"
    echo "     Frontend: cd frontend && npm run dev"
    echo ""
    echo "  3. Or use Docker:"
    echo "     docker-compose up"
    echo ""
    echo "🌐 Access the application:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend:  http://localhost:8000"
    echo "  API Docs: http://localhost:8000/docs"
    echo ""
    echo "📚 Additional commands:"
    echo "  npm run build    - Build all projects"
    echo "  npm run test     - Run all tests"
    echo "  npm run lint     - Lint all code"
    echo "  npm run clean    - Clean all build artifacts"
    echo ""
}

# Main setup function
main() {
    echo "🔧 ChainScope Setup Script"
    echo "=========================="
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! check_node_version; then
        print_error "Please install Node.js 18.0.0 or higher"
        exit 1
    fi
    
    if ! check_npm_version; then
        print_error "Please install npm"
        exit 1
    fi
    
    check_docker
    
    # Setup process
    setup_env_files
    install_dependencies
    build_shared
    
    # Optional database setup
    read -p "Do you want to set up the database with Docker? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    fi
    
    # Optional test run
    read -p "Do you want to run tests? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    display_instructions
}

# Run main function
main "$@"
