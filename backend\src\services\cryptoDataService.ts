import axios from 'axios';
import { logger } from '../utils/logger';
import { redis } from '../index';

export interface CoinData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface MarketData {
  active_cryptocurrencies: number;
  upcoming_icos: number;
  ongoing_icos: number;
  ended_icos: number;
  markets: number;
  total_market_cap: { [key: string]: number };
  total_volume: { [key: string]: number };
  market_cap_percentage: { [key: string]: number };
  market_cap_change_percentage_24h_usd: number;
  updated_at: number;
}

export class CryptoDataService {
  private readonly COINGECKO_BASE_URL = 'https://api.coingecko.com/api/v3';
  private readonly COINCAP_BASE_URL = 'https://api.coincap.io/v2';
  private readonly CACHE_TTL = 300; // 5 minutes
  private dataCollectionInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.setupAxiosInterceptors();
  }

  private setupAxiosInterceptors() {
    // Add request interceptor for rate limiting
    axios.interceptors.request.use(
      (config) => {
        // Add delay between requests to respect rate limits
        return new Promise((resolve) => {
          setTimeout(() => resolve(config), 100);
        });
      },
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error('API request failed:', {
          url: error.config?.url,
          status: error.response?.status,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get market overview data from CoinGecko
   */
  async getMarketOverview(): Promise<MarketData | null> {
    try {
      const cacheKey = 'market:overview';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/global`);
      const marketData = response.data.data;

      // Cache the data
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(marketData));
      
      logger.info('Market overview data fetched and cached');
      return marketData;
    } catch (error) {
      logger.error('Failed to fetch market overview:', error);
      return null;
    }
  }

  /**
   * Get top cryptocurrencies by market cap
   */
  async getTopCryptocurrencies(limit: number = 100): Promise<CoinData[]> {
    try {
      const cacheKey = `coins:top:${limit}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/coins/markets`, {
        params: {
          vs_currency: 'usd',
          order: 'market_cap_desc',
          per_page: limit,
          page: 1,
          sparkline: false,
          price_change_percentage: '24h'
        }
      });

      const coins = response.data;

      // Cache the data
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(coins));
      
      logger.info(`Top ${limit} cryptocurrencies fetched and cached`);
      return coins;
    } catch (error) {
      logger.error('Failed to fetch top cryptocurrencies:', error);
      return [];
    }
  }

  /**
   * Get specific coin data by ID
   */
  async getCoinData(coinId: string): Promise<any | null> {
    try {
      const cacheKey = `coin:${coinId}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/coins/${coinId}`, {
        params: {
          localization: false,
          tickers: false,
          market_data: true,
          community_data: false,
          developer_data: false,
          sparkline: false
        }
      });

      const coinData = response.data;

      // Cache the data
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(coinData));
      
      logger.info(`Coin data for ${coinId} fetched and cached`);
      return coinData;
    } catch (error) {
      logger.error(`Failed to fetch coin data for ${coinId}:`, error);
      return null;
    }
  }

  /**
   * Get historical price data for a coin
   */
  async getHistoricalPrices(coinId: string, days: number = 30): Promise<any[]> {
    try {
      const cacheKey = `history:${coinId}:${days}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/coins/${coinId}/market_chart`, {
        params: {
          vs_currency: 'usd',
          days: days,
          interval: days > 90 ? 'daily' : 'hourly'
        }
      });

      const historicalData = response.data;

      // Cache the data for longer period for historical data
      await redis.setEx(cacheKey, this.CACHE_TTL * 4, JSON.stringify(historicalData));
      
      logger.info(`Historical data for ${coinId} (${days} days) fetched and cached`);
      return historicalData;
    } catch (error) {
      logger.error(`Failed to fetch historical data for ${coinId}:`, error);
      return [];
    }
  }

  /**
   * Get trending coins
   */
  async getTrendingCoins(): Promise<any[]> {
    try {
      const cacheKey = 'coins:trending';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/search/trending`);
      const trending = response.data.coins;

      // Cache the data
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(trending));
      
      logger.info('Trending coins fetched and cached');
      return trending;
    } catch (error) {
      logger.error('Failed to fetch trending coins:', error);
      return [];
    }
  }

  /**
   * Start periodic data collection
   */
  async startDataCollection(): Promise<void> {
    logger.info('Starting crypto data collection...');
    
    // Initial data fetch
    await this.collectAllData();

    // Set up periodic data collection every 5 minutes
    this.dataCollectionInterval = setInterval(async () => {
      await this.collectAllData();
    }, 5 * 60 * 1000);

    logger.info('Crypto data collection started');
  }

  /**
   * Stop data collection
   */
  stopDataCollection(): void {
    if (this.dataCollectionInterval) {
      clearInterval(this.dataCollectionInterval);
      this.dataCollectionInterval = null;
      logger.info('Crypto data collection stopped');
    }
  }

  /**
   * Collect all essential data
   */
  private async collectAllData(): Promise<void> {
    try {
      logger.info('Collecting crypto data...');
      
      // Collect data in parallel
      await Promise.allSettled([
        this.getMarketOverview(),
        this.getTopCryptocurrencies(100),
        this.getTrendingCoins()
      ]);

      logger.info('Crypto data collection completed');
    } catch (error) {
      logger.error('Error during data collection:', error);
    }
  }

  /**
   * Get exchange rates for multiple currencies
   */
  async getExchangeRates(): Promise<any> {
    try {
      const cacheKey = 'exchange:rates';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const response = await axios.get(`${this.COINGECKO_BASE_URL}/exchange_rates`);
      const rates = response.data.rates;

      // Cache the data
      await redis.setEx(cacheKey, this.CACHE_TTL, JSON.stringify(rates));
      
      logger.info('Exchange rates fetched and cached');
      return rates;
    } catch (error) {
      logger.error('Failed to fetch exchange rates:', error);
      return {};
    }
  }
}
