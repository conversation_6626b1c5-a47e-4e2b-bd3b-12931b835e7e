import express from 'express';
import { logger } from '../utils/logger';

const router = express.Router();

// In-memory storage for demo purposes
// In a real application, this would be stored in a database
const portfolios = new Map<string, any>();

/**
 * GET /api/v1/portfolio
 * Get user's portfolio
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.headers['user-id'] as string || 'demo-user';
    
    logger.info(`Fetching portfolio for user: ${userId}`);
    
    const portfolio = portfolios.get(userId) || {
      user_id: userId,
      holdings: [],
      total_value: 0,
      total_cost: 0,
      total_pnl: 0,
      total_pnl_percentage: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    res.json({
      portfolio,
      last_updated: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error fetching portfolio:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch portfolio'
    });
  }
});

/**
 * POST /api/v1/portfolio/add
 * Add a new holding to portfolio
 */
router.post('/add', async (req, res) => {
  try {
    const userId = req.headers['user-id'] as string || 'demo-user';
    const { coin_id, symbol, name, amount, purchase_price, purchase_date } = req.body;

    // Validation
    if (!coin_id || !symbol || !name || !amount || !purchase_price) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Missing required fields: coin_id, symbol, name, amount, purchase_price'
      });
    }

    if (amount <= 0 || purchase_price <= 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Amount and purchase_price must be positive numbers'
      });
    }

    logger.info(`Adding holding to portfolio for user: ${userId}`);

    // Get or create portfolio
    let portfolio = portfolios.get(userId) || {
      user_id: userId,
      holdings: [],
      total_value: 0,
      total_cost: 0,
      total_pnl: 0,
      total_pnl_percentage: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Create new holding
    const holding = {
      id: `holding_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      coin_id,
      symbol: symbol.toUpperCase(),
      name,
      amount: parseFloat(amount),
      purchase_price: parseFloat(purchase_price),
      purchase_date: purchase_date || new Date().toISOString(),
      current_price: parseFloat(purchase_price), // Will be updated by price service
      current_value: parseFloat(amount) * parseFloat(purchase_price),
      cost_basis: parseFloat(amount) * parseFloat(purchase_price),
      pnl: 0,
      pnl_percentage: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add holding to portfolio
    portfolio.holdings.push(holding);
    
    // Recalculate portfolio totals
    portfolio = recalculatePortfolioTotals(portfolio);
    portfolio.updated_at = new Date().toISOString();

    // Save portfolio
    portfolios.set(userId, portfolio);

    res.status(201).json({
      message: 'Holding added successfully',
      holding,
      portfolio_summary: {
        total_holdings: portfolio.holdings.length,
        total_value: portfolio.total_value,
        total_cost: portfolio.total_cost,
        total_pnl: portfolio.total_pnl,
        total_pnl_percentage: portfolio.total_pnl_percentage
      }
    });
  } catch (error) {
    logger.error('Error adding portfolio holding:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to add portfolio holding'
    });
  }
});

/**
 * PUT /api/v1/portfolio/:id
 * Update a portfolio holding
 */
router.put('/:id', async (req, res) => {
  try {
    const userId = req.headers['user-id'] as string || 'demo-user';
    const { id } = req.params;
    const { amount, purchase_price, purchase_date } = req.body;

    logger.info(`Updating holding ${id} for user: ${userId}`);

    const portfolio = portfolios.get(userId);
    if (!portfolio) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Portfolio not found'
      });
    }

    const holdingIndex = portfolio.holdings.findIndex((h: any) => h.id === id);
    if (holdingIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Holding not found'
      });
    }

    // Update holding
    const holding = portfolio.holdings[holdingIndex];
    if (amount !== undefined) holding.amount = parseFloat(amount);
    if (purchase_price !== undefined) holding.purchase_price = parseFloat(purchase_price);
    if (purchase_date !== undefined) holding.purchase_date = purchase_date;
    
    // Recalculate holding values
    holding.cost_basis = holding.amount * holding.purchase_price;
    holding.current_value = holding.amount * holding.current_price;
    holding.pnl = holding.current_value - holding.cost_basis;
    holding.pnl_percentage = holding.cost_basis > 0 ? (holding.pnl / holding.cost_basis) * 100 : 0;
    holding.updated_at = new Date().toISOString();

    // Recalculate portfolio totals
    const updatedPortfolio = recalculatePortfolioTotals(portfolio);
    updatedPortfolio.updated_at = new Date().toISOString();

    // Save portfolio
    portfolios.set(userId, updatedPortfolio);

    res.json({
      message: 'Holding updated successfully',
      holding,
      portfolio_summary: {
        total_holdings: updatedPortfolio.holdings.length,
        total_value: updatedPortfolio.total_value,
        total_cost: updatedPortfolio.total_cost,
        total_pnl: updatedPortfolio.total_pnl,
        total_pnl_percentage: updatedPortfolio.total_pnl_percentage
      }
    });
  } catch (error) {
    logger.error('Error updating portfolio holding:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update portfolio holding'
    });
  }
});

/**
 * DELETE /api/v1/portfolio/:id
 * Remove a holding from portfolio
 */
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.headers['user-id'] as string || 'demo-user';
    const { id } = req.params;

    logger.info(`Removing holding ${id} for user: ${userId}`);

    const portfolio = portfolios.get(userId);
    if (!portfolio) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Portfolio not found'
      });
    }

    const holdingIndex = portfolio.holdings.findIndex((h: any) => h.id === id);
    if (holdingIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Holding not found'
      });
    }

    // Remove holding
    const removedHolding = portfolio.holdings.splice(holdingIndex, 1)[0];

    // Recalculate portfolio totals
    const updatedPortfolio = recalculatePortfolioTotals(portfolio);
    updatedPortfolio.updated_at = new Date().toISOString();

    // Save portfolio
    portfolios.set(userId, updatedPortfolio);

    res.json({
      message: 'Holding removed successfully',
      removed_holding: removedHolding,
      portfolio_summary: {
        total_holdings: updatedPortfolio.holdings.length,
        total_value: updatedPortfolio.total_value,
        total_cost: updatedPortfolio.total_cost,
        total_pnl: updatedPortfolio.total_pnl,
        total_pnl_percentage: updatedPortfolio.total_pnl_percentage
      }
    });
  } catch (error) {
    logger.error('Error removing portfolio holding:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove portfolio holding'
    });
  }
});

/**
 * GET /api/v1/portfolio/performance
 * Get portfolio performance analytics
 */
router.get('/performance', async (req, res) => {
  try {
    const userId = req.headers['user-id'] as string || 'demo-user';
    const { period = '30d' } = req.query;

    logger.info(`Fetching portfolio performance for user: ${userId}`);

    const portfolio = portfolios.get(userId);
    if (!portfolio || portfolio.holdings.length === 0) {
      return res.json({
        performance: {
          total_return: 0,
          total_return_percentage: 0,
          best_performer: null,
          worst_performer: null,
          allocation: [],
          performance_history: []
        },
        period,
        last_updated: new Date().toISOString()
      });
    }

    // Calculate performance metrics
    const totalReturn = portfolio.total_pnl;
    const totalReturnPercentage = portfolio.total_pnl_percentage;

    // Find best and worst performers
    const sortedHoldings = [...portfolio.holdings].sort((a, b) => b.pnl_percentage - a.pnl_percentage);
    const bestPerformer = sortedHoldings[0];
    const worstPerformer = sortedHoldings[sortedHoldings.length - 1];

    // Calculate allocation
    const allocation = portfolio.holdings.map((holding: any) => ({
      coin_id: holding.coin_id,
      symbol: holding.symbol,
      name: holding.name,
      percentage: portfolio.total_value > 0 ? (holding.current_value / portfolio.total_value) * 100 : 0,
      value: holding.current_value
    }));

    // Generate mock performance history (in a real app, this would come from historical data)
    const performanceHistory = generateMockPerformanceHistory(portfolio, period as string);

    const performance = {
      total_return: totalReturn,
      total_return_percentage: totalReturnPercentage,
      best_performer: bestPerformer ? {
        coin_id: bestPerformer.coin_id,
        symbol: bestPerformer.symbol,
        name: bestPerformer.name,
        return_percentage: bestPerformer.pnl_percentage,
        return_value: bestPerformer.pnl
      } : null,
      worst_performer: worstPerformer ? {
        coin_id: worstPerformer.coin_id,
        symbol: worstPerformer.symbol,
        name: worstPerformer.name,
        return_percentage: worstPerformer.pnl_percentage,
        return_value: worstPerformer.pnl
      } : null,
      allocation,
      performance_history: performanceHistory
    };

    res.json({
      performance,
      period,
      last_updated: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error fetching portfolio performance:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch portfolio performance'
    });
  }
});

/**
 * Helper function to recalculate portfolio totals
 */
function recalculatePortfolioTotals(portfolio: any): any {
  let totalValue = 0;
  let totalCost = 0;

  portfolio.holdings.forEach((holding: any) => {
    totalValue += holding.current_value;
    totalCost += holding.cost_basis;
  });

  const totalPnl = totalValue - totalCost;
  const totalPnlPercentage = totalCost > 0 ? (totalPnl / totalCost) * 100 : 0;

  return {
    ...portfolio,
    total_value: totalValue,
    total_cost: totalCost,
    total_pnl: totalPnl,
    total_pnl_percentage: totalPnlPercentage
  };
}

/**
 * Helper function to generate mock performance history
 */
function generateMockPerformanceHistory(portfolio: any, period: string): any[] {
  const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
  const history = [];
  const currentValue = portfolio.total_value;
  
  for (let i = days; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Generate mock historical value with some randomness
    const randomFactor = 0.95 + Math.random() * 0.1; // ±5% variation
    const historicalValue = currentValue * randomFactor;
    
    history.push({
      date: date.toISOString().split('T')[0],
      value: Math.round(historicalValue * 100) / 100,
      pnl: Math.round((historicalValue - portfolio.total_cost) * 100) / 100,
      pnl_percentage: portfolio.total_cost > 0 ? 
        Math.round(((historicalValue - portfolio.total_cost) / portfolio.total_cost) * 10000) / 100 : 0
    });
  }
  
  return history;
}

export default router;
