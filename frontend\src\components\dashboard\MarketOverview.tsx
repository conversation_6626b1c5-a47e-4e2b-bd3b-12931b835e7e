'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  FireIcon
} from '@heroicons/react/24/outline';

interface MarketData {
  total_market_cap: number;
  total_volume: number;
  market_cap_change_24h: number;
  btc_dominance: number;
  active_cryptocurrencies: number;
  fear_greed_index: number;
}

interface TopCoin {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  price_change_percentage_24h: number;
  market_cap: number;
  market_cap_rank: number;
}

const MarketOverview: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [topCoins, setTopCoins] = useState<TopCoin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchMarketData();
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchMarketData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchMarketData = async () => {
    try {
      setError(null);
      
      // Mock data for demonstration - replace with actual API calls
      const mockMarketData: MarketData = {
        total_market_cap: 2100000000000, // $2.1T
        total_volume: 89200000000, // $89.2B
        market_cap_change_24h: 2.5,
        btc_dominance: 52.3,
        active_cryptocurrencies: 13847,
        fear_greed_index: 72
      };

      const mockTopCoins: TopCoin[] = [
        {
          id: 'bitcoin',
          name: 'Bitcoin',
          symbol: 'BTC',
          current_price: 45234.67,
          price_change_percentage_24h: 3.2,
          market_cap: 890000000000,
          market_cap_rank: 1
        },
        {
          id: 'ethereum',
          name: 'Ethereum',
          symbol: 'ETH',
          current_price: 2834.45,
          price_change_percentage_24h: -1.8,
          market_cap: 340000000000,
          market_cap_rank: 2
        },
        {
          id: 'binancecoin',
          name: 'BNB',
          symbol: 'BNB',
          current_price: 312.89,
          price_change_percentage_24h: 4.7,
          market_cap: 48000000000,
          market_cap_rank: 3
        },
        {
          id: 'solana',
          name: 'Solana',
          symbol: 'SOL',
          current_price: 98.76,
          price_change_percentage_24h: -2.3,
          market_cap: 44000000000,
          market_cap_rank: 4
        },
        {
          id: 'cardano',
          name: 'Cardano',
          symbol: 'ADA',
          current_price: 0.52,
          price_change_percentage_24h: 1.9,
          market_cap: 18000000000,
          market_cap_rank: 5
        }
      ];

      setMarketData(mockMarketData);
      setTopCoins(mockTopCoins);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch market data');
      setLoading(false);
    }
  };

  const formatCurrency = (value: number, decimals: number = 2): string => {
    if (value >= 1e12) {
      return `$${(value / 1e12).toFixed(decimals)}T`;
    } else if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(decimals)}B`;
    } else if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(decimals)}M`;
    } else {
      return `$${value.toLocaleString(undefined, { minimumFractionDigits: decimals, maximumFractionDigits: decimals })}`;
    }
  };

  const formatPercentage = (value: number): string => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getFearGreedColor = (index: number): string => {
    if (index >= 75) return 'text-green-600';
    if (index >= 55) return 'text-yellow-600';
    if (index >= 45) return 'text-orange-600';
    if (index >= 25) return 'text-red-600';
    return 'text-red-800';
  };

  const getFearGreedLabel = (index: number): string => {
    if (index >= 75) return 'Extreme Greed';
    if (index >= 55) return 'Greed';
    if (index >= 45) return 'Neutral';
    if (index >= 25) return 'Fear';
    return 'Extreme Fear';
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-6">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p className="text-red-800 dark:text-red-200">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Market Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Market Cap */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <GlobeAltIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Market Cap
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {marketData && formatCurrency(marketData.total_market_cap)}
              </p>
              <p className={`text-sm flex items-center ${
                marketData && marketData.market_cap_change_24h >= 0 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {marketData && marketData.market_cap_change_24h >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                {marketData && formatPercentage(marketData.market_cap_change_24h)}
              </p>
            </div>
          </div>
        </div>

        {/* 24h Volume */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                24h Volume
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {marketData && formatCurrency(marketData.total_volume)}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Across all exchanges
              </p>
            </div>
          </div>
        </div>

        {/* BTC Dominance */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                BTC Dominance
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {marketData && `${marketData.btc_dominance}%`}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Market share
              </p>
            </div>
          </div>
        </div>

        {/* Fear & Greed Index */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FireIcon className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Fear & Greed
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {marketData && marketData.fear_greed_index}
              </p>
              <p className={`text-sm font-medium ${
                marketData && getFearGreedColor(marketData.fear_greed_index)
              }`}>
                {marketData && getFearGreedLabel(marketData.fear_greed_index)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Cryptocurrencies */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Top Cryptocurrencies
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Rank
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  24h Change
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Market Cap
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {topCoins.map((coin) => (
                <tr key={coin.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {coin.market_cap_rank}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                            {coin.symbol.slice(0, 2)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {coin.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {coin.symbol}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(coin.current_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center text-sm font-medium ${
                      coin.price_change_percentage_24h >= 0 
                        ? 'text-green-600' 
                        : 'text-red-600'
                    }`}>
                      {coin.price_change_percentage_24h >= 0 ? (
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                      )}
                      {formatPercentage(coin.price_change_percentage_24h)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(coin.market_cap)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default MarketOverview;
