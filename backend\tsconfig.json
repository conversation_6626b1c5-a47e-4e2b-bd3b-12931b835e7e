{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": false, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/services/*": ["services/*"], "@/models/*": ["models/*"], "@/utils/*": ["utils/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/config/*": ["config/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}