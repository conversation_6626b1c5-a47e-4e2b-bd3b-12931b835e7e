import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { CryptoDataService } from './cryptoDataService';
import { AnalyticsEngine } from './analyticsEngine';

export interface WebSocketUser {
  id: string;
  socket: Socket;
  subscribedCoins: string[];
  subscribedAlerts: string[];
}

export class WebSocketService {
  private io: Server;
  private users: Map<string, WebSocketUser> = new Map();
  private cryptoDataService: CryptoDataService;
  private analyticsEngine: AnalyticsEngine;
  private priceUpdateInterval: NodeJS.Timeout | null = null;
  private alertCheckInterval: NodeJS.Timeout | null = null;

  constructor(io: Server) {
    this.io = io;
    this.cryptoDataService = new CryptoDataService();
    this.analyticsEngine = new AnalyticsEngine();
    this.setupSocketHandlers();
    this.startPriceUpdates();
    this.startAlertChecking();
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // Add user to tracking
      const user: WebSocketUser = {
        id: socket.id,
        socket,
        subscribedCoins: [],
        subscribedAlerts: []
      };
      this.users.set(socket.id, user);

      // Handle coin subscription
      socket.on('subscribe_coins', (coinIds: string[]) => {
        this.handleCoinSubscription(socket.id, coinIds);
      });

      // Handle coin unsubscription
      socket.on('unsubscribe_coins', (coinIds: string[]) => {
        this.handleCoinUnsubscription(socket.id, coinIds);
      });

      // Handle alert subscription
      socket.on('subscribe_alerts', (alertConfig: any) => {
        this.handleAlertSubscription(socket.id, alertConfig);
      });

      // Handle alert unsubscription
      socket.on('unsubscribe_alerts', (alertIds: string[]) => {
        this.handleAlertUnsubscription(socket.id, alertIds);
      });

      // Handle real-time analysis request
      socket.on('request_analysis', async (coinId: string) => {
        await this.handleAnalysisRequest(socket.id, coinId);
      });

      // Handle portfolio updates
      socket.on('portfolio_update', (portfolioData: any) => {
        this.handlePortfolioUpdate(socket.id, portfolioData);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
        this.users.delete(socket.id);
      });

      // Send initial connection confirmation
      socket.emit('connected', {
        message: 'Connected to ChainScope WebSocket',
        timestamp: new Date().toISOString()
      });
    });
  }

  private handleCoinSubscription(userId: string, coinIds: string[]): void {
    const user = this.users.get(userId);
    if (!user) return;

    // Add new coin subscriptions
    coinIds.forEach(coinId => {
      if (!user.subscribedCoins.includes(coinId)) {
        user.subscribedCoins.push(coinId);
      }
    });

    logger.info(`User ${userId} subscribed to coins: ${coinIds.join(', ')}`);
    
    // Send confirmation
    user.socket.emit('subscription_confirmed', {
      type: 'coins',
      subscribed: coinIds,
      total_subscriptions: user.subscribedCoins.length
    });
  }

  private handleCoinUnsubscription(userId: string, coinIds: string[]): void {
    const user = this.users.get(userId);
    if (!user) return;

    // Remove coin subscriptions
    user.subscribedCoins = user.subscribedCoins.filter(
      coinId => !coinIds.includes(coinId)
    );

    logger.info(`User ${userId} unsubscribed from coins: ${coinIds.join(', ')}`);
    
    // Send confirmation
    user.socket.emit('unsubscription_confirmed', {
      type: 'coins',
      unsubscribed: coinIds,
      remaining_subscriptions: user.subscribedCoins.length
    });
  }

  private handleAlertSubscription(userId: string, alertConfig: any): void {
    const user = this.users.get(userId);
    if (!user) return;

    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const alert = {
      id: alertId,
      ...alertConfig,
      userId,
      created_at: new Date().toISOString()
    };

    user.subscribedAlerts.push(alertId);

    logger.info(`User ${userId} created alert: ${alertId}`);
    
    // Send confirmation
    user.socket.emit('alert_created', {
      alert_id: alertId,
      config: alertConfig,
      status: 'active'
    });
  }

  private handleAlertUnsubscription(userId: string, alertIds: string[]): void {
    const user = this.users.get(userId);
    if (!user) return;

    // Remove alert subscriptions
    user.subscribedAlerts = user.subscribedAlerts.filter(
      alertId => !alertIds.includes(alertId)
    );

    logger.info(`User ${userId} removed alerts: ${alertIds.join(', ')}`);
    
    // Send confirmation
    user.socket.emit('alerts_removed', {
      removed_alerts: alertIds,
      remaining_alerts: user.subscribedAlerts.length
    });
  }

  private async handleAnalysisRequest(userId: string, coinId: string): Promise<void> {
    const user = this.users.get(userId);
    if (!user) return;

    try {
      logger.info(`User ${userId} requested analysis for ${coinId}`);

      // Fetch coin data and historical prices
      const [coinData, historicalData] = await Promise.all([
        this.cryptoDataService.getCoinData(coinId),
        this.cryptoDataService.getHistoricalPrices(coinId, 30)
      ]);

      if (!coinData || !historicalData) {
        user.socket.emit('analysis_error', {
          coinId,
          error: 'Unable to fetch coin data'
        });
        return;
      }

      // Format coin data for analytics
      const formattedCoinData = {
        id: coinData.id,
        symbol: coinData.symbol,
        name: coinData.name,
        current_price: coinData.market_data?.current_price?.usd || 0,
        market_cap: coinData.market_data?.market_cap?.usd || 0,
        market_cap_rank: coinData.market_cap_rank || 0,
        fully_diluted_valuation: coinData.market_data?.fully_diluted_valuation?.usd || 0,
        total_volume: coinData.market_data?.total_volume?.usd || 0,
        high_24h: coinData.market_data?.high_24h?.usd || 0,
        low_24h: coinData.market_data?.low_24h?.usd || 0,
        price_change_24h: coinData.market_data?.price_change_24h || 0,
        price_change_percentage_24h: coinData.market_data?.price_change_percentage_24h || 0,
        market_cap_change_24h: coinData.market_data?.market_cap_change_24h || 0,
        market_cap_change_percentage_24h: coinData.market_data?.market_cap_change_percentage_24h || 0,
        circulating_supply: coinData.market_data?.circulating_supply || 0,
        total_supply: coinData.market_data?.total_supply || 0,
        max_supply: coinData.market_data?.max_supply || 0,
        ath: coinData.market_data?.ath?.usd || 0,
        ath_change_percentage: coinData.market_data?.ath_change_percentage?.usd || 0,
        ath_date: coinData.market_data?.ath_date?.usd || '',
        atl: coinData.market_data?.atl?.usd || 0,
        atl_change_percentage: coinData.market_data?.atl_change_percentage?.usd || 0,
        atl_date: coinData.market_data?.atl_date?.usd || '',
        last_updated: coinData.last_updated || ''
      };

      // Generate analysis
      const trendAnalysis = this.analyticsEngine.analyzeTrend(historicalData.prices || []);
      const tradingSignal = this.analyticsEngine.generateTradingSignal(formattedCoinData, historicalData.prices || []);
      const whaleMovements = this.analyticsEngine.detectWhaleMovements(formattedCoinData);

      // Calculate technical indicators
      const prices = (historicalData.prices || []).map((price: any) => price[1]);
      const rsi = this.analyticsEngine.calculateRSI(prices);
      const macd = this.analyticsEngine.calculateMACD(prices);

      // Send analysis results
      user.socket.emit('analysis_result', {
        coinId,
        coin_name: coinData.name,
        current_price: formattedCoinData.current_price,
        technical_indicators: {
          rsi,
          macd,
          support_level: trendAnalysis.support_level,
          resistance_level: trendAnalysis.resistance_level
        },
        trend_analysis: trendAnalysis,
        trading_signal: tradingSignal,
        whale_movements: whaleMovements,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error(`Error processing analysis request for ${coinId}:`, error);
      user.socket.emit('analysis_error', {
        coinId,
        error: 'Analysis processing failed'
      });
    }
  }

  private handlePortfolioUpdate(userId: string, portfolioData: any): void {
    const user = this.users.get(userId);
    if (!user) return;

    logger.info(`User ${userId} updated portfolio`);
    
    // Broadcast portfolio update confirmation
    user.socket.emit('portfolio_updated', {
      status: 'success',
      timestamp: new Date().toISOString()
    });
  }

  private startPriceUpdates(): void {
    // Update prices every 30 seconds
    this.priceUpdateInterval = setInterval(async () => {
      await this.broadcastPriceUpdates();
    }, 30000);

    logger.info('Price update broadcasting started');
  }

  private async broadcastPriceUpdates(): Promise<void> {
    try {
      // Get all unique subscribed coins
      const subscribedCoins = new Set<string>();
      this.users.forEach(user => {
        user.subscribedCoins.forEach(coinId => subscribedCoins.add(coinId));
      });

      if (subscribedCoins.size === 0) return;

      // Fetch current prices for subscribed coins
      const coinIds = Array.from(subscribedCoins);
      const pricePromises = coinIds.map(async (coinId) => {
        try {
          const coinData = await this.cryptoDataService.getCoinData(coinId);
          if (coinData && coinData.market_data) {
            return {
              id: coinId,
              symbol: coinData.symbol,
              name: coinData.name,
              current_price: coinData.market_data.current_price.usd,
              price_change_24h: coinData.market_data.price_change_percentage_24h,
              volume_24h: coinData.market_data.total_volume.usd,
              market_cap: coinData.market_data.market_cap.usd,
              last_updated: coinData.last_updated
            };
          }
          return null;
        } catch (error) {
          logger.error(`Error fetching price for ${coinId}:`, error);
          return null;
        }
      });

      const priceUpdates = (await Promise.all(pricePromises)).filter(update => update !== null);

      // Broadcast to subscribed users
      this.users.forEach(user => {
        const userUpdates = priceUpdates.filter(update => 
          user.subscribedCoins.includes(update!.id)
        );

        if (userUpdates.length > 0) {
          user.socket.emit('price_update', {
            updates: userUpdates,
            timestamp: new Date().toISOString()
          });
        }
      });

    } catch (error) {
      logger.error('Error broadcasting price updates:', error);
    }
  }

  private startAlertChecking(): void {
    // Check alerts every 60 seconds
    this.alertCheckInterval = setInterval(async () => {
      await this.checkAlerts();
    }, 60000);

    logger.info('Alert checking started');
  }

  private async checkAlerts(): Promise<void> {
    // Simplified alert checking - in a real implementation,
    // you would store alerts in a database and check against current prices
    this.users.forEach(user => {
      if (user.subscribedAlerts.length > 0) {
        // Placeholder for alert checking logic
        // This would check stored alert conditions against current market data
      }
    });
  }

  public stop(): void {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
      this.priceUpdateInterval = null;
    }

    if (this.alertCheckInterval) {
      clearInterval(this.alertCheckInterval);
      this.alertCheckInterval = null;
    }

    logger.info('WebSocket service stopped');
  }
}
