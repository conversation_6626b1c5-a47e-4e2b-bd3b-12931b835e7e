// ChainScope - Cryptocurrency Analytics Platform
// Prisma Schema for PostgreSQL Database

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  avatar    String?
  isActive  Boolean  @default(true)
  role      UserRole @default(USER)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?

  // Relations
  portfolios    Portfolio[]
  alerts        Alert[]
  watchlists    Watchlist[]
  apiKeys       ApiKey[]
  sessions      UserSession[]

  @@map("users")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  token        String   @unique
  refreshToken String?  @unique
  expiresAt    DateTime
  isActive     Boolean  @default(true)
  ipAddress    String?
  userAgent    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model ApiKey {
  id          String    @id @default(cuid())
  userId      String
  name        String
  key         String    @unique
  permissions String[]  @default([])
  isActive    Boolean   @default(true)
  lastUsed    DateTime?
  expiresAt   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// Cryptocurrency Data
model Cryptocurrency {
  id              String  @id @default(cuid())
  coinId          String  @unique // CoinGecko/CoinCap ID
  symbol          String
  name            String
  slug            String?
  description     String?
  website         String?
  whitepaper      String?
  sourceCode      String?
  logo            String?

  // Market Data
  currentPrice    Decimal?
  marketCap       Decimal?
  volume24h       Decimal?
  circulatingSupply Decimal?
  totalSupply     Decimal?
  maxSupply       Decimal?

  // Rankings
  marketCapRank   Int?
  volumeRank      Int?

  // Metadata
  isActive        Boolean @default(true)
  category        String?
  blockchain      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  priceHistory     PriceHistory[]
  marketData       MarketData[]
  portfolioItems   PortfolioItem[]
  watchlistItems   WatchlistItem[]
  alerts           Alert[]
  onChainMetrics   OnChainMetrics[]

  @@map("cryptocurrencies")
}

// Price and Market Data
model PriceHistory {
  id              String   @id @default(cuid())
  cryptoId        String
  price           Decimal
  marketCap       Decimal?
  volume24h       Decimal?
  timestamp       DateTime
  source          String   @default("coingecko")

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp, source])
  @@map("price_history")
}

model MarketData {
  id                    String   @id @default(cuid())
  cryptoId              String

  // Price metrics
  currentPrice          Decimal
  priceChange24h        Decimal?
  priceChangePercent24h Decimal?
  priceChange7d         Decimal?
  priceChangePercent7d  Decimal?
  priceChange30d        Decimal?
  priceChangePercent30d Decimal?

  // Market metrics
  marketCap             Decimal?
  marketCapChange24h    Decimal?
  volume24h             Decimal?
  volumeChange24h       Decimal?

  // Supply metrics
  circulatingSupply     Decimal?
  totalSupply           Decimal?
  maxSupply             Decimal?

  // Technical indicators
  rsi                   Decimal?
  macd                  Decimal?
  movingAverage50       Decimal?
  movingAverage200      Decimal?

  // Sentiment
  fearGreedIndex        Int?
  socialSentiment       Decimal?

  timestamp DateTime @default(now())

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp])
  @@map("market_data")
}

// On-Chain Analytics
model OnChainMetrics {
  id                    String   @id @default(cuid())
  cryptoId              String

  // Transaction metrics
  transactionCount24h   BigInt?
  transactionVolume24h  Decimal?
  avgTransactionValue   Decimal?
  avgTransactionFee     Decimal?

  // Address metrics
  activeAddresses24h    BigInt?
  newAddresses24h       BigInt?
  totalAddresses        BigInt?

  // Network metrics
  hashRate              Decimal?
  difficulty            Decimal?
  blockTime             Decimal?
  blockSize             Decimal?

  // Whale metrics
  whaleTransactions24h  Int?
  largeTransactionValue Decimal?

  // DeFi metrics (if applicable)
  tvl                   Decimal?
  stakingRatio          Decimal?

  timestamp DateTime @default(now())

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp])
  @@map("onchain_metrics")
}

// Portfolio Management
model Portfolio {
  id          String  @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean @default(false)
  isPublic    Boolean @default(false)

  // Performance metrics
  totalValue     Decimal @default(0)
  totalCost      Decimal @default(0)
  totalPnL       Decimal @default(0)
  totalPnLPercent Decimal @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  items PortfolioItem[]

  @@map("portfolios")
}

model PortfolioItem {
  id           String  @id @default(cuid())
  portfolioId  String
  cryptoId     String

  // Holdings
  quantity     Decimal
  avgBuyPrice  Decimal
  totalCost    Decimal
  currentValue Decimal @default(0)
  pnl          Decimal @default(0)
  pnlPercent   Decimal @default(0)

  // Metadata
  notes        String?
  tags         String[] @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  portfolio      Portfolio      @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)
  transactions   Transaction[]

  @@unique([portfolioId, cryptoId])
  @@map("portfolio_items")
}

model Transaction {
  id              String          @id @default(cuid())
  portfolioItemId String
  type            TransactionType
  quantity        Decimal
  price           Decimal
  totalAmount     Decimal
  fee             Decimal         @default(0)
  exchange        String?
  notes           String?

  transactionDate DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  portfolioItem PortfolioItem @relation(fields: [portfolioItemId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

// Watchlists and Alerts
model Watchlist {
  id          String  @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  items WatchlistItem[]

  @@map("watchlists")
}

model WatchlistItem {
  id          String @id @default(cuid())
  watchlistId String
  cryptoId    String
  notes       String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  watchlist      Watchlist      @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([watchlistId, cryptoId])
  @@map("watchlist_items")
}

model Alert {
  id          String    @id @default(cuid())
  userId      String
  cryptoId    String
  name        String
  description String?

  // Alert conditions
  type        AlertType
  condition   AlertCondition
  targetValue Decimal
  currentValue Decimal?

  // Alert settings
  isActive    Boolean @default(true)
  isTriggered Boolean @default(false)
  triggerCount Int    @default(0)
  maxTriggers Int?

  // Notification settings
  emailNotification Boolean @default(true)
  pushNotification  Boolean @default(false)
  webhookUrl        String?

  // Timestamps
  triggeredAt DateTime?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@map("alerts")
}

// Analytics and Insights
model TradingSignal {
  id          String       @id @default(cuid())
  cryptoId    String
  type        SignalType
  strength    SignalStrength
  confidence  Decimal      // 0-100

  // Signal data
  indicator   String       // RSI, MACD, etc.
  timeframe   String       // 1h, 4h, 1d, etc.
  price       Decimal
  targetPrice Decimal?
  stopLoss    Decimal?

  // Metadata
  description String?
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  expiresAt DateTime?

  @@map("trading_signals")
}

model MarketInsight {
  id          String      @id @default(cuid())
  title       String
  content     String
  category    InsightCategory
  importance  InsightImportance

  // Related data
  cryptoIds   String[]    @default([])
  tags        String[]    @default([])

  // Metadata
  isPublished Boolean     @default(false)
  publishedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("market_insights")
}

// System and Configuration
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  ConfigType @default(STRING)

  description String?
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  resourceId String?

  // Request data
  ipAddress String?
  userAgent String?

  // Change data
  oldValues Json?
  newValues Json?

  createdAt DateTime @default(now())

  @@map("audit_logs")
}

// Enums
enum UserRole {
  USER
  PREMIUM
  ADMIN
  SUPER_ADMIN
}

enum TransactionType {
  BUY
  SELL
  TRANSFER_IN
  TRANSFER_OUT
  STAKE
  UNSTAKE
  REWARD
  AIRDROP
}

enum AlertType {
  PRICE_ABOVE
  PRICE_BELOW
  PRICE_CHANGE_PERCENT
  VOLUME_SPIKE
  MARKET_CAP_CHANGE
  WHALE_MOVEMENT
  TECHNICAL_INDICATOR
}

enum AlertCondition {
  GREATER_THAN
  LESS_THAN
  EQUALS
  PERCENTAGE_CHANGE
}

enum SignalType {
  BUY
  SELL
  HOLD
  STRONG_BUY
  STRONG_SELL
}

enum SignalStrength {
  WEAK
  MODERATE
  STRONG
  VERY_STRONG
}

enum InsightCategory {
  MARKET_ANALYSIS
  TECHNICAL_ANALYSIS
  NEWS
  REGULATORY
  TECHNOLOGY
  DEFI
  NFT
}

enum InsightImportance {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ConfigType {
  STRING
  NUMBER
  BOOLEAN
  JSON
}
