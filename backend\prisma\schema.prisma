// ChainScope - Cryptocurrency Analytics Platform
// Prisma Schema for PostgreSQL Database

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  avatar    String?
  isActive  Boolean  @default(true)
  role      UserRole @default(USER)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?

  // Relations
  portfolios    Portfolio[]
  alerts        Alert[]
  watchlists    Watchlist[]
  apiKeys       ApiKey[]
  sessions      UserSession[]

  @@map("users")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  token        String   @unique
  refreshToken String?  @unique
  expiresAt    DateTime
  isActive     Boolean  @default(true)
  ipAddress    String?
  userAgent    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model ApiKey {
  id          String    @id @default(cuid())
  userId      String
  name        String
  key         String    @unique
  permissions String[]  @default([])
  isActive    Boolean   @default(true)
  lastUsed    DateTime?
  expiresAt   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// Cryptocurrency Data
model Cryptocurrency {
  id              String  @id @default(cuid())
  coinId          String  @unique // CoinGecko/CoinCap ID
  symbol          String
  name            String
  slug            String?
  description     String?
  website         String?
  whitepaper      String?
  sourceCode      String?
  logo            String?

  // Market Data
  currentPrice    Decimal?
  marketCap       Decimal?
  volume24h       Decimal?
  circulatingSupply Decimal?
  totalSupply     Decimal?
  maxSupply       Decimal?

  // Rankings
  marketCapRank   Int?
  volumeRank      Int?

  // Metadata
  isActive        Boolean @default(true)
  category        String?
  blockchain      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  priceHistory     PriceHistory[]
  marketData       MarketData[]
  portfolioItems   PortfolioItem[]
  watchlistItems   WatchlistItem[]
  alerts           Alert[]
  onChainMetrics   OnChainMetrics[]

  @@map("cryptocurrencies")
}

// Price and Market Data
model PriceHistory {
  id              String   @id @default(cuid())
  cryptoId        String
  price           Decimal
  marketCap       Decimal?
  volume24h       Decimal?
  timestamp       DateTime
  source          String   @default("coingecko")

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp, source])
  @@map("price_history")
}

model MarketData {
  id                    String   @id @default(cuid())
  cryptoId              String

  // Price metrics
  currentPrice          Decimal
  priceChange24h        Decimal?
  priceChangePercent24h Decimal?
  priceChange7d         Decimal?
  priceChangePercent7d  Decimal?
  priceChange30d        Decimal?
  priceChangePercent30d Decimal?

  // Market metrics
  marketCap             Decimal?
  marketCapChange24h    Decimal?
  volume24h             Decimal?
  volumeChange24h       Decimal?

  // Supply metrics
  circulatingSupply     Decimal?
  totalSupply           Decimal?
  maxSupply             Decimal?

  // Technical indicators
  rsi                   Decimal?
  macd                  Decimal?
  movingAverage50       Decimal?
  movingAverage200      Decimal?

  // Sentiment
  fearGreedIndex        Int?
  socialSentiment       Decimal?

  timestamp DateTime @default(now())

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp])
  @@map("market_data")
}

// On-Chain Analytics
model OnChainMetrics {
  id                    String   @id @default(cuid())
  cryptoId              String

  // Transaction metrics
  transactionCount24h   BigInt?
  transactionVolume24h  Decimal?
  avgTransactionValue   Decimal?
  avgTransactionFee     Decimal?

  // Address metrics
  activeAddresses24h    BigInt?
  newAddresses24h       BigInt?
  totalAddresses        BigInt?

  // Network metrics
  hashRate              Decimal?
  difficulty            Decimal?
  blockTime             Decimal?
  blockSize             Decimal?

  // Whale metrics
  whaleTransactions24h  Int?
  largeTransactionValue Decimal?

  // DeFi metrics (if applicable)
  tvl                   Decimal?
  stakingRatio          Decimal?

  timestamp DateTime @default(now())

  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)

  @@unique([cryptoId, timestamp])
  @@map("onchain_metrics")
}

// Portfolio Management
model Portfolio {
  id          String  @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean @default(false)
  isPublic    Boolean @default(false)

  // Performance metrics
  totalValue     Decimal @default(0)
  totalCost      Decimal @default(0)
  totalPnL       Decimal @default(0)
  totalPnLPercent Decimal @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user  User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  items PortfolioItem[]

  @@map("portfolios")
}

model PortfolioItem {
  id           String  @id @default(cuid())
  portfolioId  String
  cryptoId     String

  // Holdings
  quantity     Decimal
  avgBuyPrice  Decimal
  totalCost    Decimal
  currentValue Decimal @default(0)
  pnl          Decimal @default(0)
  pnlPercent   Decimal @default(0)

  // Metadata
  notes        String?
  tags         String[] @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  portfolio      Portfolio      @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  cryptocurrency Cryptocurrency @relation(fields: [cryptoId], references: [id], onDelete: Cascade)
  transactions   Transaction[]

  @@unique([portfolioId, cryptoId])
  @@map("portfolio_items")
}
