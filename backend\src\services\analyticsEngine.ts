import { logger } from '../utils/logger';
import { CoinData } from './cryptoDataService';

export interface MarketSignal {
  type: 'BUY' | 'SELL' | 'HOLD' | 'STRONG_BUY' | 'STRONG_SELL';
  confidence: number; // 0-100
  reason: string;
  timestamp: Date;
  coinId: string;
  price: number;
}

export interface TrendAnalysis {
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  strength: number; // 0-100
  duration: number; // days
  support_level: number;
  resistance_level: number;
  next_target: number;
}

export interface WhaleMovement {
  coinId: string;
  amount: number;
  direction: 'IN' | 'OUT';
  exchange?: string;
  timestamp: Date;
  impact_score: number; // 0-100
}

export interface MarketSentiment {
  overall_sentiment: 'EXTREMELY_BULLISH' | 'BULLISH' | 'NEUTRAL' | 'BEARISH' | 'EXTREMELY_BEARISH';
  fear_greed_index: number; // 0-100
  social_sentiment: number; // 0-100
  news_sentiment: number; // 0-100
  technical_sentiment: number; // 0-100
}

export class AnalyticsEngine {
  private readonly RSI_PERIOD = 14;
  private readonly MACD_FAST = 12;
  private readonly MACD_SLOW = 26;
  private readonly MACD_SIGNAL = 9;

  /**
   * Calculate Relative Strength Index (RSI)
   */
  calculateRSI(prices: number[]): number {
    if (prices.length < this.RSI_PERIOD + 1) {
      return 50; // Neutral RSI if not enough data
    }

    let gains = 0;
    let losses = 0;

    // Calculate initial average gain and loss
    for (let i = 1; i <= this.RSI_PERIOD; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }

    let avgGain = gains / this.RSI_PERIOD;
    let avgLoss = losses / this.RSI_PERIOD;

    // Calculate RSI for remaining periods
    for (let i = this.RSI_PERIOD + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      const gain = change > 0 ? change : 0;
      const loss = change < 0 ? Math.abs(change) : 0;

      avgGain = (avgGain * (this.RSI_PERIOD - 1) + gain) / this.RSI_PERIOD;
      avgLoss = (avgLoss * (this.RSI_PERIOD - 1) + loss) / this.RSI_PERIOD;
    }

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  /**
   * Calculate Moving Average Convergence Divergence (MACD)
   */
  calculateMACD(prices: number[]): { macd: number; signal: number; histogram: number } {
    if (prices.length < this.MACD_SLOW) {
      return { macd: 0, signal: 0, histogram: 0 };
    }

    const ema12 = this.calculateEMA(prices, this.MACD_FAST);
    const ema26 = this.calculateEMA(prices, this.MACD_SLOW);
    const macd = ema12 - ema26;

    // Calculate signal line (EMA of MACD)
    const macdValues = [macd]; // In real implementation, you'd have historical MACD values
    const signal = this.calculateEMA(macdValues, this.MACD_SIGNAL);
    const histogram = macd - signal;

    return { macd, signal, histogram };
  }

  /**
   * Calculate Exponential Moving Average (EMA)
   */
  private calculateEMA(prices: number[], period: number): number {
    if (prices.length === 0) return 0;
    if (prices.length === 1) return prices[0];

    const multiplier = 2 / (period + 1);
    let ema = prices[0];

    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  /**
   * Calculate Simple Moving Average (SMA)
   */
  private calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return 0;
    
    const sum = prices.slice(-period).reduce((acc, price) => acc + price, 0);
    return sum / period;
  }

  /**
   * Detect support and resistance levels
   */
  detectSupportResistance(prices: number[]): { support: number; resistance: number } {
    if (prices.length < 20) {
      return { support: Math.min(...prices), resistance: Math.max(...prices) };
    }

    const sortedPrices = [...prices].sort((a, b) => a - b);
    const support = sortedPrices[Math.floor(sortedPrices.length * 0.1)]; // 10th percentile
    const resistance = sortedPrices[Math.floor(sortedPrices.length * 0.9)]; // 90th percentile

    return { support, resistance };
  }

  /**
   * Analyze market trend
   */
  analyzeTrend(historicalData: any[]): TrendAnalysis {
    if (!historicalData || historicalData.length === 0) {
      return {
        trend: 'SIDEWAYS',
        strength: 0,
        duration: 0,
        support_level: 0,
        resistance_level: 0,
        next_target: 0
      };
    }

    const prices = historicalData.map(data => data[1]); // Assuming [timestamp, price] format
    const currentPrice = prices[prices.length - 1];
    
    // Calculate moving averages
    const sma20 = this.calculateSMA(prices, 20);
    const sma50 = this.calculateSMA(prices, 50);
    
    // Determine trend direction
    let trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS' = 'SIDEWAYS';
    let strength = 0;

    if (currentPrice > sma20 && sma20 > sma50) {
      trend = 'BULLISH';
      strength = Math.min(100, ((currentPrice - sma50) / sma50) * 100);
    } else if (currentPrice < sma20 && sma20 < sma50) {
      trend = 'BEARISH';
      strength = Math.min(100, ((sma50 - currentPrice) / sma50) * 100);
    } else {
      strength = 25; // Sideways market
    }

    // Calculate support and resistance
    const { support, resistance } = this.detectSupportResistance(prices);
    
    // Calculate next target based on trend
    let nextTarget = currentPrice;
    if (trend === 'BULLISH') {
      nextTarget = resistance + (resistance - support) * 0.618; // Fibonacci extension
    } else if (trend === 'BEARISH') {
      nextTarget = support - (resistance - support) * 0.618;
    }

    return {
      trend,
      strength: Math.abs(strength),
      duration: this.calculateTrendDuration(prices),
      support_level: support,
      resistance_level: resistance,
      next_target: nextTarget
    };
  }

  /**
   * Calculate trend duration in days
   */
  private calculateTrendDuration(prices: number[]): number {
    // Simplified trend duration calculation
    // In a real implementation, you'd analyze price movements more thoroughly
    let duration = 1;
    const currentPrice = prices[prices.length - 1];
    
    for (let i = prices.length - 2; i >= 0; i--) {
      const prevPrice = prices[i];
      const priceChange = Math.abs(currentPrice - prevPrice) / prevPrice;
      
      if (priceChange > 0.05) { // 5% change indicates trend continuation
        duration++;
      } else {
        break;
      }
    }
    
    return Math.min(duration, prices.length);
  }

  /**
   * Generate trading signals based on technical analysis
   */
  generateTradingSignal(coinData: CoinData, historicalData: any[]): MarketSignal {
    const prices = historicalData.map(data => data[1]);
    const currentPrice = coinData.current_price;
    
    // Calculate technical indicators
    const rsi = this.calculateRSI(prices);
    const macd = this.calculateMACD(prices);
    const trendAnalysis = this.analyzeTrend(historicalData);
    
    let signalType: MarketSignal['type'] = 'HOLD';
    let confidence = 0;
    let reason = '';

    // RSI-based signals
    if (rsi < 30) {
      signalType = 'BUY';
      confidence += 30;
      reason += 'Oversold RSI condition. ';
    } else if (rsi > 70) {
      signalType = 'SELL';
      confidence += 30;
      reason += 'Overbought RSI condition. ';
    }

    // MACD-based signals
    if (macd.histogram > 0 && macd.macd > macd.signal) {
      if (signalType === 'BUY') {
        signalType = 'STRONG_BUY';
        confidence += 25;
      } else if (signalType === 'HOLD') {
        signalType = 'BUY';
        confidence += 20;
      }
      reason += 'Bullish MACD crossover. ';
    } else if (macd.histogram < 0 && macd.macd < macd.signal) {
      if (signalType === 'SELL') {
        signalType = 'STRONG_SELL';
        confidence += 25;
      } else if (signalType === 'HOLD') {
        signalType = 'SELL';
        confidence += 20;
      }
      reason += 'Bearish MACD crossover. ';
    }

    // Trend-based signals
    if (trendAnalysis.trend === 'BULLISH' && trendAnalysis.strength > 60) {
      confidence += 20;
      reason += 'Strong bullish trend. ';
    } else if (trendAnalysis.trend === 'BEARISH' && trendAnalysis.strength > 60) {
      confidence += 20;
      reason += 'Strong bearish trend. ';
    }

    // Price action signals
    const priceChange24h = coinData.price_change_percentage_24h;
    if (Math.abs(priceChange24h) > 10) {
      if (priceChange24h > 10) {
        reason += 'Significant price increase (potential reversal). ';
      } else {
        reason += 'Significant price decrease (potential bounce). ';
      }
      confidence += 15;
    }

    // Ensure confidence is within bounds
    confidence = Math.min(100, Math.max(0, confidence));

    return {
      type: signalType,
      confidence,
      reason: reason.trim() || 'No clear signal detected',
      timestamp: new Date(),
      coinId: coinData.id,
      price: currentPrice
    };
  }

  /**
   * Calculate market sentiment based on various factors
   */
  calculateMarketSentiment(marketData: any, topCoins: CoinData[]): MarketSentiment {
    let technicalSentiment = 50; // Neutral starting point
    let overallSentiment: MarketSentiment['overall_sentiment'] = 'NEUTRAL';

    // Calculate technical sentiment based on top coins performance
    if (topCoins.length > 0) {
      const avgPriceChange = topCoins.reduce((sum, coin) => 
        sum + coin.price_change_percentage_24h, 0) / topCoins.length;
      
      technicalSentiment = 50 + (avgPriceChange * 2); // Scale to 0-100
      technicalSentiment = Math.max(0, Math.min(100, technicalSentiment));
    }

    // Determine overall sentiment
    if (technicalSentiment >= 80) {
      overallSentiment = 'EXTREMELY_BULLISH';
    } else if (technicalSentiment >= 60) {
      overallSentiment = 'BULLISH';
    } else if (technicalSentiment >= 40) {
      overallSentiment = 'NEUTRAL';
    } else if (technicalSentiment >= 20) {
      overallSentiment = 'BEARISH';
    } else {
      overallSentiment = 'EXTREMELY_BEARISH';
    }

    // Calculate Fear & Greed Index (simplified)
    const fearGreedIndex = technicalSentiment;

    return {
      overall_sentiment: overallSentiment,
      fear_greed_index: fearGreedIndex,
      social_sentiment: 50, // Placeholder - would integrate social media APIs
      news_sentiment: 50, // Placeholder - would integrate news sentiment APIs
      technical_sentiment: technicalSentiment
    };
  }

  /**
   * Detect potential whale movements (simplified simulation)
   */
  detectWhaleMovements(coinData: CoinData): WhaleMovement[] {
    const movements: WhaleMovement[] = [];
    
    // Simulate whale detection based on volume and price changes
    if (coinData.total_volume > coinData.market_cap * 0.1) { // High volume relative to market cap
      const impactScore = Math.min(100, (coinData.total_volume / coinData.market_cap) * 100);
      
      movements.push({
        coinId: coinData.id,
        amount: coinData.total_volume * 0.05, // Estimate 5% of volume as whale activity
        direction: coinData.price_change_24h > 0 ? 'IN' : 'OUT',
        timestamp: new Date(),
        impact_score: impactScore
      });
    }

    return movements;
  }
}
