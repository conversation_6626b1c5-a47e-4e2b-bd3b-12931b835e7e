import express from 'express';
import { CryptoDataService } from '../services/cryptoDataService';
import { AnalyticsEngine } from '../services/analyticsEngine';
import { logger } from '../utils/logger';

const router = express.Router();
const cryptoDataService = new CryptoDataService();
const analyticsEngine = new AnalyticsEngine();

/**
 * GET /api/v1/analytics/metrics
 * Get comprehensive blockchain metrics and analytics
 */
router.get('/metrics', async (req, res) => {
  try {
    const { coins = 'bitcoin,ethereum,binancecoin,cardano,solana' } = req.query;
    const coinIds = typeof coins === 'string' ? coins.split(',') : ['bitcoin'];

    logger.info(`Fetching analytics metrics for coins: ${coinIds.join(', ')}`);

    const metricsPromises = coinIds.map(async (coinId) => {
      try {
        const [coinData, historicalData] = await Promise.all([
          cryptoDataService.getCoinData(coinId.trim()),
          cryptoDataService.getHistoricalPrices(coinId.trim(), 30)
        ]);

        if (!coinData || !historicalData) {
          return null;
        }

        // Convert coin data to the format expected by analytics engine
        const formattedCoinData = {
          id: coinData.id,
          symbol: coinData.symbol,
          name: coinData.name,
          current_price: coinData.market_data?.current_price?.usd || 0,
          market_cap: coinData.market_data?.market_cap?.usd || 0,
          market_cap_rank: coinData.market_cap_rank || 0,
          fully_diluted_valuation: coinData.market_data?.fully_diluted_valuation?.usd || 0,
          total_volume: coinData.market_data?.total_volume?.usd || 0,
          high_24h: coinData.market_data?.high_24h?.usd || 0,
          low_24h: coinData.market_data?.low_24h?.usd || 0,
          price_change_24h: coinData.market_data?.price_change_24h || 0,
          price_change_percentage_24h: coinData.market_data?.price_change_percentage_24h || 0,
          market_cap_change_24h: coinData.market_data?.market_cap_change_24h || 0,
          market_cap_change_percentage_24h: coinData.market_data?.market_cap_change_percentage_24h || 0,
          circulating_supply: coinData.market_data?.circulating_supply || 0,
          total_supply: coinData.market_data?.total_supply || 0,
          max_supply: coinData.market_data?.max_supply || 0,
          ath: coinData.market_data?.ath?.usd || 0,
          ath_change_percentage: coinData.market_data?.ath_change_percentage?.usd || 0,
          ath_date: coinData.market_data?.ath_date?.usd || '',
          atl: coinData.market_data?.atl?.usd || 0,
          atl_change_percentage: coinData.market_data?.atl_change_percentage?.usd || 0,
          atl_date: coinData.market_data?.atl_date?.usd || '',
          last_updated: coinData.last_updated || ''
        };

        // Generate comprehensive analytics with safe data access
        const historicalPrices = Array.isArray(historicalData) && historicalData.length > 0 ? historicalData : [];
        const trendAnalysis = analyticsEngine.analyzeTrend(historicalPrices);
        const tradingSignal = analyticsEngine.generateTradingSignal(formattedCoinData, historicalPrices);
        const whaleMovements = analyticsEngine.detectWhaleMovements(formattedCoinData);

        // Calculate technical indicators
        const prices = historicalPrices.map((price: any) => Array.isArray(price) ? price[1] : price);
        const rsi = analyticsEngine.calculateRSI(prices);
        const macd = analyticsEngine.calculateMACD(prices);

        return {
          coin_id: coinId,
          coin_name: coinData.name,
          coin_symbol: coinData.symbol,
          current_price: formattedCoinData.current_price,
          market_cap: formattedCoinData.market_cap,
          volume_24h: formattedCoinData.total_volume,
          price_change_24h: formattedCoinData.price_change_percentage_24h,
          technical_indicators: {
            rsi: rsi,
            macd: macd,
            support_level: trendAnalysis.support_level,
            resistance_level: trendAnalysis.resistance_level
          },
          trend_analysis: trendAnalysis,
          trading_signal: tradingSignal,
          whale_movements: whaleMovements,
          risk_metrics: {
            volatility: Math.abs(formattedCoinData.price_change_percentage_24h),
            liquidity_score: Math.min(100, (formattedCoinData.total_volume / formattedCoinData.market_cap) * 100),
            market_dominance: 0 // Would calculate based on total market cap
          }
        };
      } catch (error) {
        logger.error(`Error processing metrics for ${coinId}:`, error);
        return null;
      }
    });

    const metrics = (await Promise.all(metricsPromises)).filter(metric => metric !== null);

    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString(),
      total_coins: metrics.length
    });
  } catch (error) {
    logger.error('Error fetching analytics metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/v1/analytics/whale-movements
 * Track large cryptocurrency transactions and whale movements
 */
router.get('/whale-movements', async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    
    logger.info('Fetching whale movements data');
    
    // Get top cryptocurrencies to analyze for whale movements
    const topCoins = await cryptoDataService.getTopCryptocurrencies(Number(limit));
    
    if (!topCoins || topCoins.length === 0) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch whale movements data at this time'
      });
    }

    // Detect whale movements for each coin
    const whaleMovements = topCoins.flatMap(coin => {
      const formattedCoin = {
        id: coin.id,
        symbol: coin.symbol,
        name: coin.name,
        current_price: coin.current_price,
        market_cap: coin.market_cap,
        market_cap_rank: coin.market_cap_rank,
        fully_diluted_valuation: coin.fully_diluted_valuation || 0,
        total_volume: coin.total_volume,
        high_24h: coin.high_24h,
        low_24h: coin.low_24h,
        price_change_24h: coin.price_change_24h,
        price_change_percentage_24h: coin.price_change_percentage_24h,
        market_cap_change_24h: coin.market_cap_change_24h,
        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
        circulating_supply: coin.circulating_supply,
        total_supply: coin.total_supply,
        max_supply: coin.max_supply,
        ath: coin.ath,
        ath_change_percentage: coin.ath_change_percentage,
        ath_date: coin.ath_date,
        atl: coin.atl,
        atl_change_percentage: coin.atl_change_percentage,
        atl_date: coin.atl_date,
        last_updated: coin.last_updated
      };
      
      return analyticsEngine.detectWhaleMovements(formattedCoin);
    });

    // Sort by impact score and limit results
    const sortedMovements = whaleMovements
      .sort((a, b) => b.impact_score - a.impact_score)
      .slice(0, 20);

    const response = {
      whale_movements: sortedMovements,
      summary: {
        total_movements: sortedMovements.length,
        high_impact_movements: sortedMovements.filter(m => m.impact_score > 70).length,
        inflow_movements: sortedMovements.filter(m => m.direction === 'IN').length,
        outflow_movements: sortedMovements.filter(m => m.direction === 'OUT').length,
        average_impact_score: sortedMovements.reduce((sum, m) => sum + m.impact_score, 0) / sortedMovements.length
      },
      last_updated: new Date().toISOString()
    };

    res.json(response);
  } catch (error) {
    logger.error('Error fetching whale movements:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch whale movements data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/v1/analytics/network-health
 * Get network health indicators for major blockchains
 */
router.get('/network-health', async (req, res) => {
  try {
    logger.info('Fetching network health indicators');
    
    // Major blockchain networks to analyze
    const networks = ['bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana', 'polkadot', 'avalanche-2'];
    
    const healthPromises = networks.map(async (networkId) => {
      try {
        const [coinData, historicalData] = await Promise.all([
          cryptoDataService.getCoinData(networkId),
          cryptoDataService.getHistoricalPrices(networkId, 7) // 7 days for network health
        ]);

        if (!coinData || !historicalData) {
          return null;
        }

        // Safe data access for historical data
        const historicalPrices = Array.isArray(historicalData) && historicalData.length > 0 ? historicalData : [];
        const prices = historicalPrices.map((price: any) => Array.isArray(price) ? price[1] : price);

        // Calculate network health metrics using helper functions
        const priceVolatility = calculateVolatility(prices);
        const volumeConsistency = calculateVolumeConsistency(prices); // Using prices as proxy for volumes
        const networkActivity = coinData.market_data?.total_volume?.usd || 0;
        const marketCapStability = Math.abs(coinData.market_data?.market_cap_change_percentage_24h || 0);

        // Calculate overall health score (0-100)
        let healthScore = 100;
        healthScore -= Math.min(30, priceVolatility); // Penalize high volatility
        healthScore -= Math.min(20, marketCapStability); // Penalize market cap instability
        healthScore += Math.min(20, volumeConsistency); // Reward consistent volume
        healthScore = Math.max(0, Math.min(100, healthScore));

        return {
          network_id: networkId,
          network_name: coinData.name,
          network_symbol: coinData.symbol,
          health_score: Math.round(healthScore),
          metrics: {
            price_volatility: Math.round(priceVolatility * 100) / 100,
            volume_consistency: Math.round(volumeConsistency * 100) / 100,
            market_cap_stability: Math.round((100 - marketCapStability) * 100) / 100,
            network_activity: networkActivity,
            transaction_volume_24h: coinData.market_data?.total_volume?.usd || 0
          },
          status: healthScore > 80 ? 'EXCELLENT' : 
                  healthScore > 60 ? 'GOOD' : 
                  healthScore > 40 ? 'FAIR' : 'POOR',
          last_updated: new Date().toISOString()
        };
      } catch (error) {
        logger.error(`Error calculating network health for ${networkId}:`, error);
        return null;
      }
    });

    const networkHealthData = (await Promise.all(healthPromises)).filter(data => data !== null);

    if (networkHealthData.length === 0) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch network health data at this time'
      });
    }

    // Sort by health score
    networkHealthData.sort((a, b) => b.health_score - a.health_score);

    const response = {
      network_health: networkHealthData,
      summary: {
        total_networks: networkHealthData.length,
        excellent_networks: networkHealthData.filter(n => n.status === 'EXCELLENT').length,
        good_networks: networkHealthData.filter(n => n.status === 'GOOD').length,
        average_health_score: Math.round(networkHealthData.reduce((sum, n) => sum + n.health_score, 0) / networkHealthData.length)
      },
      last_updated: new Date().toISOString()
    };

    res.json(response);
  } catch (error) {
    logger.error('Error fetching network health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch network health data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper method to calculate price volatility
function calculateVolatility(prices: number[]): number {
  if (prices.length < 2) return 0;
  
  const returns = [];
  for (let i = 1; i < prices.length; i++) {
    returns.push((prices[i] - prices[i-1]) / prices[i-1]);
  }
  
  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
  
  return Math.sqrt(variance) * 100; // Convert to percentage
}

// Helper method to calculate volume consistency
function calculateVolumeConsistency(volumes: number[]): number {
  if (volumes.length < 2) return 0;
  
  const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
  const deviations = volumes.map(v => Math.abs(v - avgVolume) / avgVolume);
  const avgDeviation = deviations.reduce((sum, d) => sum + d, 0) / deviations.length;
  
  return Math.max(0, 100 - (avgDeviation * 100)); // Higher consistency = lower deviation
}

export default router;
