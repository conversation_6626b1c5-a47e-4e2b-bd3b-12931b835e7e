// Shared TypeScript types for ChainScope platform

export interface CoinData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface MarketData {
  active_cryptocurrencies: number;
  upcoming_icos: number;
  ongoing_icos: number;
  ended_icos: number;
  markets: number;
  total_market_cap: { [key: string]: number };
  total_volume: { [key: string]: number };
  market_cap_percentage: { [key: string]: number };
  market_cap_change_percentage_24h_usd: number;
  updated_at: number;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  support_level: number;
  resistance_level: number;
}

export interface TrendAnalysis {
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  strength: number; // 0-100
  duration: number; // days
  support_level: number;
  resistance_level: number;
  next_target: number;
}

export interface MarketSignal {
  type: 'BUY' | 'SELL' | 'HOLD' | 'STRONG_BUY' | 'STRONG_SELL';
  confidence: number; // 0-100
  reason: string;
  timestamp: Date;
  coinId: string;
  price: number;
}

export interface WhaleMovement {
  coinId: string;
  amount: number;
  direction: 'IN' | 'OUT';
  exchange?: string;
  timestamp: Date;
  impact_score: number; // 0-100
}

export interface MarketSentiment {
  overall_sentiment: 'EXTREMELY_BULLISH' | 'BULLISH' | 'NEUTRAL' | 'BEARISH' | 'EXTREMELY_BEARISH';
  fear_greed_index: number; // 0-100
  social_sentiment: number; // 0-100
  news_sentiment: number; // 0-100
  technical_sentiment: number; // 0-100
}

export interface PortfolioHolding {
  id: string;
  coin_id: string;
  symbol: string;
  name: string;
  amount: number;
  purchase_price: number;
  purchase_date: string;
  current_price: number;
  current_value: number;
  cost_basis: number;
  pnl: number;
  pnl_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface Portfolio {
  user_id: string;
  holdings: PortfolioHolding[];
  total_value: number;
  total_cost: number;
  total_pnl: number;
  total_pnl_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface AuthResponse {
  message: string;
  user: User;
  token: string;
  expires_in: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  last_updated?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    per_page: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
}

export interface PriceUpdate {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  price_change_24h: number;
  volume_24h: number;
  market_cap: number;
  last_updated: string;
}

export interface AlertConfig {
  coin_id: string;
  condition: 'ABOVE' | 'BELOW' | 'CHANGE_PERCENT';
  value: number;
  message?: string;
  enabled: boolean;
}

export interface Alert {
  id: string;
  user_id: string;
  config: AlertConfig;
  triggered: boolean;
  triggered_at?: string;
  created_at: string;
  updated_at: string;
}

export interface NetworkHealth {
  network_id: string;
  network_name: string;
  network_symbol: string;
  health_score: number; // 0-100
  metrics: {
    price_volatility: number;
    volume_consistency: number;
    market_cap_stability: number;
    network_activity: number;
    transaction_volume_24h: number;
  };
  status: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  last_updated: string;
}

export interface AnalyticsMetrics {
  coin_id: string;
  coin_name: string;
  coin_symbol: string;
  current_price: number;
  market_cap: number;
  volume_24h: number;
  price_change_24h: number;
  technical_indicators: TechnicalIndicators;
  trend_analysis: TrendAnalysis;
  trading_signal: MarketSignal;
  whale_movements: WhaleMovement[];
  risk_metrics: {
    volatility: number;
    liquidity_score: number;
    market_dominance: number;
  };
}

export interface PerformanceData {
  date: string;
  value: number;
  pnl: number;
  pnl_percentage: number;
}

export interface PortfolioPerformance {
  total_return: number;
  total_return_percentage: number;
  best_performer: {
    coin_id: string;
    symbol: string;
    name: string;
    return_percentage: number;
    return_value: number;
  } | null;
  worst_performer: {
    coin_id: string;
    symbol: string;
    name: string;
    return_percentage: number;
    return_value: number;
  } | null;
  allocation: Array<{
    coin_id: string;
    symbol: string;
    name: string;
    percentage: number;
    value: number;
  }>;
  performance_history: PerformanceData[];
}

// Chart data types
export interface ChartDataPoint {
  x: number | string | Date;
  y: number;
}

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
  }>;
}

// API Error types
export interface ApiError {
  error: string;
  message: string;
  status?: number;
  timestamp?: string;
}

// WebSocket event types
export type WebSocketEventType = 
  | 'connected'
  | 'price_update'
  | 'alert_triggered'
  | 'analysis_result'
  | 'analysis_error'
  | 'portfolio_updated'
  | 'subscription_confirmed'
  | 'unsubscription_confirmed'
  | 'alert_created'
  | 'alerts_removed';

export interface WebSocketEvent {
  type: WebSocketEventType;
  data: any;
  timestamp: string;
}
