{"name": "chainscope", "version": "1.0.0", "description": "ChainScope - Advanced Cryptocurrency On-Chain Analytics Platform", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:frontend -- --fix && npm run lint:backend -- --fix", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "clean": "npm run clean:frontend && npm run clean:backend && npm run clean:shared", "clean:frontend": "cd frontend && rm -rf .next node_modules", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:shared": "cd shared && rm -rf dist node_modules", "install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:shared", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:shared": "cd shared && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --rmi all", "setup": "npm run install:all && npm run build:shared", "reset": "npm run clean && npm run setup"}, "keywords": ["cryptocurrency", "blockchain", "analytics", "on-chain", "trading", "defi", "bitcoin", "ethereum", "market-data", "technical-analysis"], "author": {"name": "HectorTa1989", "url": "https://github.com/HectorTa1989"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/chainscope.git"}, "bugs": {"url": "https://github.com/HectorTa1989/chainscope/issues"}, "homepage": "https://github.com/HectorTa1989/chainscope#readme", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}}