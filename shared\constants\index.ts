// Shared constants for ChainScope platform

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    REGISTER: '/api/v1/auth/register',
    REFRESH: '/api/v1/auth/refresh',
    PROFILE: '/api/v1/auth/profile',
    CHANGE_PASSWORD: '/api/v1/auth/change-password',
  },
  
  // Market data
  MARKET: {
    OVERVIEW: '/api/v1/market/overview',
    PRICES: '/api/v1/market/prices',
    COIN: '/api/v1/market/coin',
    TRENDING: '/api/v1/market/trending',
    SEARCH: '/api/v1/market/search',
    EXCHANGE_RATES: '/api/v1/market/exchange-rates',
  },
  
  // Analytics
  ANALYTICS: {
    METRICS: '/api/v1/analytics/metrics',
    WHALE_MOVEMENTS: '/api/v1/analytics/whale-movements',
    NETWORK_HEALTH: '/api/v1/analytics/network-health',
  },
  
  // Portfolio
  PORTFOLIO: {
    BASE: '/api/v1/portfolio',
    ADD: '/api/v1/portfolio/add',
    PERFORMANCE: '/api/v1/portfolio/performance',
  },
} as const;

export const WEBSOCKET_EVENTS = {
  // Connection events
  CONNECTED: 'connected',
  DISCONNECT: 'disconnect',
  
  // Subscription events
  SUBSCRIBE_COINS: 'subscribe_coins',
  UNSUBSCRIBE_COINS: 'unsubscribe_coins',
  SUBSCRIBE_ALERTS: 'subscribe_alerts',
  UNSUBSCRIBE_ALERTS: 'unsubscribe_alerts',
  
  // Data events
  PRICE_UPDATE: 'price_update',
  ALERT_TRIGGERED: 'alert_triggered',
  ANALYSIS_RESULT: 'analysis_result',
  ANALYSIS_ERROR: 'analysis_error',
  
  // Portfolio events
  PORTFOLIO_UPDATE: 'portfolio_update',
  PORTFOLIO_UPDATED: 'portfolio_updated',
  
  // Request events
  REQUEST_ANALYSIS: 'request_analysis',
  
  // Confirmation events
  SUBSCRIPTION_CONFIRMED: 'subscription_confirmed',
  UNSUBSCRIPTION_CONFIRMED: 'unsubscription_confirmed',
  ALERT_CREATED: 'alert_created',
  ALERTS_REMOVED: 'alerts_removed',
} as const;

export const COIN_CATEGORIES = {
  LAYER_1: 'layer-1',
  LAYER_2: 'layer-2',
  DEFI: 'decentralized-finance-defi',
  NFT: 'non-fungible-tokens-nft',
  GAMING: 'gaming',
  METAVERSE: 'metaverse',
  MEME: 'meme-token',
  STABLECOIN: 'stablecoins',
  EXCHANGE: 'exchange-based-tokens',
  PRIVACY: 'privacy-coins',
} as const;

export const MARKET_SENTIMENT_LEVELS = {
  EXTREMELY_BEARISH: { min: 0, max: 20, color: '#dc2626', label: 'Extremely Bearish' },
  BEARISH: { min: 20, max: 40, color: '#ea580c', label: 'Bearish' },
  NEUTRAL: { min: 40, max: 60, color: '#ca8a04', label: 'Neutral' },
  BULLISH: { min: 60, max: 80, color: '#16a34a', label: 'Bullish' },
  EXTREMELY_BULLISH: { min: 80, max: 100, color: '#059669', label: 'Extremely Bullish' },
} as const;

export const TRADING_SIGNAL_COLORS = {
  STRONG_BUY: '#059669',
  BUY: '#16a34a',
  HOLD: '#ca8a04',
  SELL: '#ea580c',
  STRONG_SELL: '#dc2626',
} as const;

export const CHART_COLORS = {
  PRIMARY: '#3b82f6',
  SUCCESS: '#10b981',
  WARNING: '#f59e0b',
  DANGER: '#ef4444',
  INFO: '#06b6d4',
  PURPLE: '#8b5cf6',
  PINK: '#ec4899',
  INDIGO: '#6366f1',
  GRAY: '#6b7280',
} as const;

export const TIME_PERIODS = {
  '1H': { label: '1 Hour', value: '1h', days: 1 },
  '24H': { label: '24 Hours', value: '24h', days: 1 },
  '7D': { label: '7 Days', value: '7d', days: 7 },
  '30D': { label: '30 Days', value: '30d', days: 30 },
  '90D': { label: '90 Days', value: '90d', days: 90 },
  '1Y': { label: '1 Year', value: '1y', days: 365 },
  'ALL': { label: 'All Time', value: 'all', days: 0 },
} as const;

export const PORTFOLIO_PERIODS = {
  '7D': { label: '7 Days', value: '7d' },
  '30D': { label: '30 Days', value: '30d' },
  '90D': { label: '90 Days', value: '90d' },
  '1Y': { label: '1 Year', value: '1y' },
} as const;

export const CURRENCY_SYMBOLS = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  BTC: '₿',
  ETH: 'Ξ',
} as const;

export const SUPPORTED_CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'BTC', symbol: '₿', name: 'Bitcoin' },
  { code: 'ETH', symbol: 'Ξ', name: 'Ethereum' },
] as const;

export const MAJOR_CRYPTOCURRENCIES = [
  { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin' },
  { id: 'ethereum', symbol: 'ETH', name: 'Ethereum' },
  { id: 'binancecoin', symbol: 'BNB', name: 'BNB' },
  { id: 'cardano', symbol: 'ADA', name: 'Cardano' },
  { id: 'solana', symbol: 'SOL', name: 'Solana' },
  { id: 'polkadot', symbol: 'DOT', name: 'Polkadot' },
  { id: 'avalanche-2', symbol: 'AVAX', name: 'Avalanche' },
  { id: 'chainlink', symbol: 'LINK', name: 'Chainlink' },
  { id: 'polygon', symbol: 'MATIC', name: 'Polygon' },
  { id: 'uniswap', symbol: 'UNI', name: 'Uniswap' },
] as const;

export const RISK_LEVELS = {
  LOW: { min: 0, max: 30, color: '#10b981', label: 'Low Risk' },
  MEDIUM: { min: 30, max: 60, color: '#f59e0b', label: 'Medium Risk' },
  HIGH: { min: 60, max: 80, color: '#ea580c', label: 'High Risk' },
  VERY_HIGH: { min: 80, max: 100, color: '#dc2626', label: 'Very High Risk' },
} as const;

export const NETWORK_STATUS = {
  EXCELLENT: { color: '#059669', label: 'Excellent' },
  GOOD: { color: '#16a34a', label: 'Good' },
  FAIR: { color: '#ca8a04', label: 'Fair' },
  POOR: { color: '#dc2626', label: 'Poor' },
} as const;

export const ALERT_CONDITIONS = {
  ABOVE: { label: 'Price Above', symbol: '>' },
  BELOW: { label: 'Price Below', symbol: '<' },
  CHANGE_PERCENT: { label: 'Price Change %', symbol: '±' },
} as const;

export const PAGINATION_LIMITS = {
  COINS: [25, 50, 100, 250],
  PORTFOLIO: [10, 25, 50],
  ALERTS: [10, 25, 50],
  TRANSACTIONS: [10, 25, 50, 100],
} as const;

export const LOCAL_STORAGE_KEYS = {
  AUTH_TOKEN: 'chainscope_auth_token',
  USER_PREFERENCES: 'chainscope_user_preferences',
  PORTFOLIO_SETTINGS: 'chainscope_portfolio_settings',
  CHART_SETTINGS: 'chainscope_chart_settings',
  WATCHLIST: 'chainscope_watchlist',
  THEME: 'chainscope_theme',
} as const;

export const THEME_OPTIONS = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

export const CHART_TYPES = {
  LINE: 'line',
  CANDLESTICK: 'candlestick',
  AREA: 'area',
  BAR: 'bar',
} as const;

export const TECHNICAL_INDICATORS = {
  RSI: { label: 'RSI', description: 'Relative Strength Index' },
  MACD: { label: 'MACD', description: 'Moving Average Convergence Divergence' },
  SMA: { label: 'SMA', description: 'Simple Moving Average' },
  EMA: { label: 'EMA', description: 'Exponential Moving Average' },
  BOLLINGER: { label: 'Bollinger Bands', description: 'Bollinger Bands' },
  VOLUME: { label: 'Volume', description: 'Trading Volume' },
} as const;

export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  RATE_LIMITED: 'Too many requests. Please wait and try again.',
} as const;

export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in!',
  LOGOUT_SUCCESS: 'Successfully logged out!',
  REGISTER_SUCCESS: 'Account created successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  PORTFOLIO_UPDATED: 'Portfolio updated successfully!',
  ALERT_CREATED: 'Alert created successfully!',
  ALERT_DELETED: 'Alert deleted successfully!',
} as const;

export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  AMOUNT_MIN: 0.********,
  PRICE_MIN: 0.********,
} as const;

export const DEFAULT_SETTINGS = {
  CURRENCY: 'USD',
  THEME: 'system',
  CHART_TYPE: 'line',
  TIME_PERIOD: '24h',
  PORTFOLIO_PERIOD: '30d',
  ITEMS_PER_PAGE: 50,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_SOUND: false,
  AUTO_REFRESH: true,
  REFRESH_INTERVAL: 30000, // 30 seconds
} as const;
