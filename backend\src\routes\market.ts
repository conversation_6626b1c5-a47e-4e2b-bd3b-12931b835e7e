import express from 'express';
import { CryptoDataService } from '../services/cryptoDataService';
import { AnalyticsEngine } from '../services/analyticsEngine';
import { logger } from '../utils/logger';

const router = express.Router();
const cryptoDataService = new CryptoDataService();
const analyticsEngine = new AnalyticsEngine();

/**
 * GET /api/v1/market/overview
 * Get market overview data
 */
router.get('/overview', async (req, res) => {
  try {
    logger.info('Fetching market overview');

    const marketData = await cryptoDataService.getMarketOverview();
    const topCoins = await cryptoDataService.getTopCryptocurrencies(10);

    if (!marketData) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch market data at this time'
      });
    }

    // Calculate market sentiment
    const sentiment = analyticsEngine.calculateMarketSentiment(marketData, topCoins);

    const response = {
      market_data: marketData,
      top_performers: topCoins.slice(0, 5),
      market_sentiment: sentiment,
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error('Error fetching market overview:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch market overview'
    });
  }
});

/**
 * GET /api/v1/market/prices
 * Get real-time cryptocurrency prices
 */
router.get('/prices', async (req, res) => {
  try {
    const { limit = 100, page = 1 } = req.query;

    logger.info(`Fetching cryptocurrency prices (limit: ${limit}, page: ${page})`);

    const coins = await cryptoDataService.getTopCryptocurrencies(Number(limit));

    if (!coins || coins.length === 0) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch price data at this time'
      });
    }

    const response = {
      coins,
      pagination: {
        current_page: Number(page),
        per_page: Number(limit),
        total_coins: coins.length
      },
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error('Error fetching cryptocurrency prices:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch cryptocurrency prices'
    });
  }
});

/**
 * GET /api/v1/market/coin/:id
 * Get detailed information for a specific coin
 */
router.get('/coin/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { days = 30 } = req.query;
    
    logger.info(`Fetching coin data for ${id}`);
    
    const [coinData, historicalData] = await Promise.all([
      cryptoDataService.getCoinData(id),
      cryptoDataService.getHistoricalPrices(id, Number(days))
    ]);
    
    if (!coinData) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Coin with ID '${id}' not found`
      });
    }

    // Generate trading signal if we have valid coin data
    let tradingSignal = null;
    let trendAnalysis = null;

    if (coinData && coinData.market_data) {
      const formattedCoinData = {
        id: coinData.id,
        symbol: coinData.symbol,
        name: coinData.name,
        current_price: coinData.market_data.current_price?.usd || 0,
        market_cap: coinData.market_data.market_cap?.usd || 0,
        market_cap_rank: coinData.market_cap_rank || 0,
        fully_diluted_valuation: coinData.market_data.fully_diluted_valuation?.usd || 0,
        total_volume: coinData.market_data.total_volume?.usd || 0,
        high_24h: coinData.market_data.high_24h?.usd || 0,
        low_24h: coinData.market_data.low_24h?.usd || 0,
        price_change_24h: coinData.market_data.price_change_24h || 0,
        price_change_percentage_24h: coinData.market_data.price_change_percentage_24h || 0,
        market_cap_change_24h: coinData.market_data.market_cap_change_24h || 0,
        market_cap_change_percentage_24h: coinData.market_data.market_cap_change_percentage_24h || 0,
        circulating_supply: coinData.market_data.circulating_supply || 0,
        total_supply: coinData.market_data.total_supply || 0,
        max_supply: coinData.market_data.max_supply || 0,
        ath: coinData.market_data.ath?.usd || 0,
        ath_change_percentage: coinData.market_data.ath_change_percentage?.usd || 0,
        ath_date: coinData.market_data.ath_date?.usd || '',
        atl: coinData.market_data.atl?.usd || 0,
        atl_change_percentage: coinData.market_data.atl_change_percentage?.usd || 0,
        atl_date: coinData.market_data.atl_date?.usd || '',
        last_updated: coinData.last_updated || ''
      };

      const prices = (historicalData as any)?.prices || [];
      tradingSignal = analyticsEngine.generateTradingSignal(formattedCoinData, prices);
      trendAnalysis = analyticsEngine.analyzeTrend(prices);
    }

    const response = {
      coin_data: coinData,
      historical_data: historicalData,
      trading_signal: tradingSignal,
      trend_analysis: trendAnalysis,
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error(`Error fetching coin data for ${req.params.id}:`, error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch coin data'
    });
  }
});

/**
 * GET /api/v1/market/trending
 * Get trending cryptocurrencies
 */
router.get('/trending', async (req, res) => {
  try {
    logger.info('Fetching trending cryptocurrencies');
    
    const trending = await cryptoDataService.getTrendingCoins();
    
    if (!trending || trending.length === 0) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch trending data at this time'
      });
    }

    const response = {
      trending_coins: trending,
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error('Error fetching trending cryptocurrencies:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch trending cryptocurrencies'
    });
  }
});

/**
 * GET /api/v1/market/search
 * Search for cryptocurrencies
 */
router.get('/search', async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || typeof q !== 'string') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Search query parameter "q" is required'
      });
    }

    logger.info(`Searching for cryptocurrencies with query: ${q}`);
    
    // Get all coins and filter by search query
    const allCoins = await cryptoDataService.getTopCryptocurrencies(250);
    
    const searchResults = allCoins.filter(coin => 
      coin.name.toLowerCase().includes(q.toLowerCase()) ||
      coin.symbol.toLowerCase().includes(q.toLowerCase()) ||
      coin.id.toLowerCase().includes(q.toLowerCase())
    ).slice(0, 20); // Limit to 20 results

    const response = {
      query: q,
      results: searchResults,
      total_results: searchResults.length,
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error('Error searching cryptocurrencies:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to search cryptocurrencies'
    });
  }
});

/**
 * GET /api/v1/market/exchange-rates
 * Get current exchange rates
 */
router.get('/exchange-rates', async (req, res) => {
  try {
    logger.info('Fetching exchange rates');
    
    const rates = await cryptoDataService.getExchangeRates();
    
    if (!rates || Object.keys(rates).length === 0) {
      return res.status(503).json({
        error: 'Service Unavailable',
        message: 'Unable to fetch exchange rates at this time'
      });
    }

    const response = {
      exchange_rates: rates,
      last_updated: new Date().toISOString()
    };

    return res.json(response);
  } catch (error) {
    logger.error('Error fetching exchange rates:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch exchange rates'
    });
  }
});

export default router;
