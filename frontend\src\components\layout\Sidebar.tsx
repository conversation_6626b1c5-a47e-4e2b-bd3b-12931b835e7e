'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BellIcon,
  Cog6ToothIcon,
  DocumentTextIcon,
  FireIcon,
  EyeIcon,
  ArrowTrendingUpIcon,
  ShieldCheckIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  CurrencyDollarIcon as CurrencyDollarIconSolid,
  BellIcon as BellIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  DocumentTextIcon as DocumentTextIconSolid,
  FireIcon as FireIconSolid,
  EyeIcon as EyeIconSolid,
  ArrowTrendingUpIcon as ArrowTrendingUpIconSolid,
  ShieldCheckIcon as ShieldCheckIconSolid,
  GlobeAltIcon as GlobeAltIconSolid
} from '@heroicons/react/24/solid';

interface SidebarProps {
  isOpen: boolean;
  onClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      current: pathname === '/'
    },
    {
      name: 'Market Overview',
      href: '/market',
      icon: ChartBarIcon,
      iconSolid: ChartBarIconSolid,
      current: pathname === '/market'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ArrowTrendingUpIcon,
      iconSolid: ArrowTrendingUpIconSolid,
      current: pathname === '/analytics'
    },
    {
      name: 'Portfolio',
      href: '/portfolio',
      icon: CurrencyDollarIcon,
      iconSolid: CurrencyDollarIconSolid,
      current: pathname === '/portfolio'
    },
    {
      name: 'Watchlist',
      href: '/watchlist',
      icon: EyeIcon,
      iconSolid: EyeIconSolid,
      current: pathname === '/watchlist'
    },
    {
      name: 'Trending',
      href: '/trending',
      icon: FireIcon,
      iconSolid: FireIconSolid,
      current: pathname === '/trending'
    },
    {
      name: 'Network Health',
      href: '/network-health',
      icon: ShieldCheckIcon,
      iconSolid: ShieldCheckIconSolid,
      current: pathname === '/network-health'
    },
    {
      name: 'Global Metrics',
      href: '/global',
      icon: GlobeAltIcon,
      iconSolid: GlobeAltIconSolid,
      current: pathname === '/global'
    }
  ];

  const secondaryNavigation = [
    {
      name: 'Alerts',
      href: '/alerts',
      icon: BellIcon,
      iconSolid: BellIconSolid,
      current: pathname === '/alerts'
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: DocumentTextIcon,
      iconSolid: DocumentTextIconSolid,
      current: pathname === '/reports'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      iconSolid: Cog6ToothIconSolid,
      current: pathname === '/settings'
    }
  ];

  const handleLinkClick = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={onClose}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Logo area - hidden on desktop since it's in header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 lg:hidden">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                ChainScope
              </span>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-8 overflow-y-auto">
            {/* Primary Navigation */}
            <div>
              <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Main
              </h3>
              <div className="mt-3 space-y-1">
                {navigation.map((item) => {
                  const Icon = item.current ? item.iconSolid : item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={handleLinkClick}
                      className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                        item.current
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                      }`}
                    >
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 ${
                          item.current
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                        }`}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </div>
            </div>

            {/* Secondary Navigation */}
            <div>
              <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tools
              </h3>
              <div className="mt-3 space-y-1">
                {secondaryNavigation.map((item) => {
                  const Icon = item.current ? item.iconSolid : item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={handleLinkClick}
                      className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                        item.current
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                      }`}
                    >
                      <Icon
                        className={`mr-3 h-5 w-5 flex-shrink-0 ${
                          item.current
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                        }`}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                Quick Stats
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    Total Market Cap
                  </span>
                  <span className="text-xs font-medium text-gray-900 dark:text-white">
                    $2.1T
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    24h Volume
                  </span>
                  <span className="text-xs font-medium text-gray-900 dark:text-white">
                    $89.2B
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    BTC Dominance
                  </span>
                  <span className="text-xs font-medium text-gray-900 dark:text-white">
                    52.3%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    Fear & Greed
                  </span>
                  <span className="text-xs font-medium text-green-600 dark:text-green-400">
                    Greed (72)
                  </span>
                </div>
              </div>
            </div>

            {/* Market Status */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="ml-2 text-sm font-medium text-green-800 dark:text-green-200">
                  Markets are open
                </span>
              </div>
              <p className="mt-1 text-xs text-green-600 dark:text-green-300">
                Real-time data streaming
              </p>
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
              <p>ChainScope v1.0.0</p>
              <p className="mt-1">© 2025 All rights reserved</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
