version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainscope-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: chainscope
      POSTGRES_USER: chainscope_user
      POSTGRES_PASSWORD: chainscope_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chainscope-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chainscope-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - chainscope-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chainscope-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      NODE_ENV: production
      PORT: 8000
      DATABASE_URL: **************************************************************/chainscope
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      FRONTEND_URL: http://localhost:3000
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - chainscope-network

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chainscope-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_WS_URL: ws://localhost:8000
    depends_on:
      - backend
    networks:
      - chainscope-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: chainscope-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - chainscope-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  chainscope-network:
    driver: bridge
